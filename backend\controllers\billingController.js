const Billing = require('../models/Billing');
const Joi = require('joi');

const billingSchema = Joi.object({
  patient_id: Joi.number().integer().positive().required(),
  appointment_id: Joi.number().integer().positive().allow(null),
  invoice_number: Joi.string().min(1).max(50).required(),
  total_amount: Joi.number().positive().required(),
  paid_amount: Joi.number().min(0).default(0),
  billing_date: Joi.date().iso().default(() => new Date().toISOString().split('T')[0]),
  due_date: Joi.date().iso().allow(null),
  payment_status: Joi.string().valid('pending', 'partial', 'paid', 'overdue', 'cancelled').default('pending'),
  payment_method: Joi.string().valid('cash', 'card', 'insurance', 'bank_transfer', 'check').allow(null),
  insurance_claim_amount: Joi.number().min(0).default(0),
  discount_amount: Joi.number().min(0).default(0),
  tax_amount: Joi.number().min(0).default(0),
  description: Joi.string().max(500).allow(null, ''),
  notes: Joi.string().max(1000).allow(null, '')
});

const updateBillingSchema = billingSchema.fork(['patient_id', 'invoice_number', 'total_amount'], (schema) => schema.optional());

const paymentSchema = Joi.object({
  amount: Joi.number().positive().required(),
  payment_method: Joi.string().valid('cash', 'card', 'insurance', 'bank_transfer', 'check').required(),
  notes: Joi.string().max(500).allow(null, '')
});

class BillingController {
  static async getAllBilling(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        payment_status, 
        patient_id, 
        date_from, 
        date_to, 
        overdue 
      } = req.query;

      const offset = (page - 1) * limit;
      const filters = {};

      if (payment_status) filters.payment_status = payment_status;
      if (patient_id) filters.patient_id = patient_id;
      if (date_from) filters.date_from = date_from;
      if (date_to) filters.date_to = date_to;
      if (overdue === 'true') filters.overdue = true;

      const billing = await Billing.findAll(parseInt(limit), offset, filters);

      res.json({
        success: true,
        data: billing,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: billing.length
        }
      });
    } catch (error) {
      console.error('Error fetching billing records:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch billing records',
        message: error.message
      });
    }
  }

  static async getBillingById(req, res) {
    try {
      const { id } = req.params;
      const billing = await Billing.findById(id);

      if (!billing) {
        return res.status(404).json({
          success: false,
          error: 'Billing record not found'
        });
      }

      res.json({
        success: true,
        data: billing
      });
    } catch (error) {
      console.error('Error fetching billing record:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch billing record',
        message: error.message
      });
    }
  }

  static async createBilling(req, res) {
    try {
      const { error, value } = billingSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const billing = await Billing.create(value);

      res.status(201).json({
        success: true,
        data: billing,
        message: 'Billing record created successfully'
      });
    } catch (error) {
      console.error('Error creating billing record:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create billing record',
        message: error.message
      });
    }
  }

  static async updateBilling(req, res) {
    try {
      const { id } = req.params;
      const { error, value } = updateBillingSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const billing = await Billing.update(id, value);

      if (!billing) {
        return res.status(404).json({
          success: false,
          error: 'Billing record not found'
        });
      }

      res.json({
        success: true,
        data: billing,
        message: 'Billing record updated successfully'
      });
    } catch (error) {
      console.error('Error updating billing record:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update billing record',
        message: error.message
      });
    }
  }

  static async deleteBilling(req, res) {
    try {
      const { id } = req.params;
      const deleted = await Billing.delete(id);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: 'Billing record not found'
        });
      }

      res.json({
        success: true,
        message: 'Billing record deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting billing record:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete billing record',
        message: error.message
      });
    }
  }

  static async processPayment(req, res) {
    try {
      const { id } = req.params;
      const { error, value } = paymentSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const billing = await Billing.processPayment(id, value);

      res.json({
        success: true,
        data: billing,
        message: 'Payment processed successfully'
      });
    } catch (error) {
      console.error('Error processing payment:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to process payment',
        message: error.message
      });
    }
  }

  static async getBillingStatistics(req, res) {
    try {
      const statistics = await Billing.getStatistics();
      const paymentMethods = await Billing.getPaymentMethodAnalytics();
      const insuranceClaims = await Billing.getInsuranceClaimAnalytics();

      res.json({
        success: true,
        data: {
          overview: statistics,
          paymentMethods: paymentMethods,
          insuranceClaims: insuranceClaims
        }
      });
    } catch (error) {
      console.error('Error fetching billing statistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch billing statistics',
        message: error.message
      });
    }
  }

  static async getRevenueAnalytics(req, res) {
    try {
      const { start_date, end_date } = req.query;

      if (!start_date || !end_date) {
        return res.status(400).json({
          success: false,
          error: 'Start date and end date are required'
        });
      }

      const revenue = await Billing.getRevenueAnalytics(start_date, end_date);

      res.json({
        success: true,
        data: revenue
      });
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch revenue analytics',
        message: error.message
      });
    }
  }

  static async getOverdueBills(req, res) {
    try {
      const overdueBills = await Billing.getOverdueBills();

      res.json({
        success: true,
        data: overdueBills
      });
    } catch (error) {
      console.error('Error fetching overdue bills:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch overdue bills',
        message: error.message
      });
    }
  }

  static async getTopDebtors(req, res) {
    try {
      const { limit = 10 } = req.query;
      const debtors = await Billing.getTopDebtors(parseInt(limit));

      res.json({
        success: true,
        data: debtors
      });
    } catch (error) {
      console.error('Error fetching top debtors:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch top debtors',
        message: error.message
      });
    }
  }
}

module.exports = BillingController;
