import 'package:flutter/material.dart';
import '../services/api_service.dart';

class BillingScreen extends StatefulWidget {
  const BillingScreen({super.key});

  @override
  State<BillingScreen> createState() => _BillingScreenState();
}

class _BillingScreenState extends State<BillingScreen> {
  List<dynamic> billingRecords = [];
  bool isLoading = true;
  String? error;
  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadBillingRecords();
  }

  Future<void> _loadBillingRecords() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final response = await ApiService.getBilling();
      setState(() {
        billingRecords = response['data'] ?? [];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  List<dynamic> get filteredBillingRecords {
    if (searchQuery.isEmpty) return billingRecords;
    return billingRecords.where((record) {
      final patientName = (record['patient_name'] ?? '').toLowerCase();
      final description = (record['description'] ?? '').toLowerCase();
      final query = searchQuery.toLowerCase();
      return patientName.contains(query) || description.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Billing'),
        backgroundColor: Colors.purple[800],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBillingRecords,
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search billing records...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, size: 64, color: Colors.red[400]),
                            const SizedBox(height: 16),
                            Text('Error: $error'),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadBillingRecords,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _buildBillingList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddBillingDialog(),
        backgroundColor: Colors.purple[800],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBillingList() {
    final displayRecords = filteredBillingRecords;
    
    if (displayRecords.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No billing records found'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadBillingRecords,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: displayRecords.length,
        itemBuilder: (context, index) {
          final record = displayRecords[index];
          return _buildBillingCard(record);
        },
      ),
    );
  }

  Widget _buildBillingCard(Map<String, dynamic> record) {
    final status = record['payment_status'] ?? 'pending';
    Color statusColor = Colors.orange;
    IconData statusIcon = Icons.pending;

    switch (status.toLowerCase()) {
      case 'paid':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'overdue':
        statusColor = Colors.red;
        statusIcon = Icons.warning;
        break;
      case 'partial':
        statusColor = Colors.blue;
        statusIcon = Icons.payment;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: statusColor.withValues(alpha: 0.1),
          child: Icon(statusIcon, color: statusColor),
        ),
        title: Text(
          record['patient_name'] ?? 'Unknown Patient',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (record['description'] != null) 
              Text('📋 ${record['description']}'),
            if (record['total_amount'] != null) 
              Text('💰 \$${record['total_amount']}'),
            if (record['billing_date'] != null) 
              Text('📅 ${record['billing_date']}'),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                status.toUpperCase(),
                style: TextStyle(
                  color: statusColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility),
                  SizedBox(width: 8),
                  Text('View Details'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            if (status != 'paid')
              const PopupMenuItem(
                value: 'pay',
                child: Row(
                  children: [
                    Icon(Icons.payment, color: Colors.green),
                    SizedBox(width: 8),
                    Text('Mark as Paid', style: TextStyle(color: Colors.green)),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          onSelected: (value) => _handleBillingAction(value, record),
        ),
        onTap: () => _showBillingDetails(record),
      ),
    );
  }

  void _handleBillingAction(String action, Map<String, dynamic> record) {
    switch (action) {
      case 'view':
        _showBillingDetails(record);
        break;
      case 'edit':
        _showEditBillingDialog(record);
        break;
      case 'pay':
        _markAsPaid(record['id']);
        break;
      case 'delete':
        _confirmDeleteBilling(record);
        break;
    }
  }

  void _showBillingDetails(Map<String, dynamic> record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Billing Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Patient', record['patient_name']),
              _buildDetailRow('Description', record['description']),
              _buildDetailRow('Total Amount', '\$${record['total_amount']}'),
              _buildDetailRow('Paid Amount', '\$${record['paid_amount'] ?? 0}'),
              _buildDetailRow('Balance', '\$${record['balance_amount'] ?? 0}'),
              _buildDetailRow('Billing Date', record['billing_date']),
              _buildDetailRow('Due Date', record['due_date']),
              _buildDetailRow('Status', record['payment_status']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, dynamic value) {
    if (value == null || value.toString().isEmpty) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value.toString())),
        ],
      ),
    );
  }

  void _showAddBillingDialog() {
    _showBillingForm();
  }

  void _showEditBillingDialog(Map<String, dynamic> record) {
    _showBillingForm(record: record);
  }

  void _showBillingForm({Map<String, dynamic>? record}) {
    final isEditing = record != null;
    final formKey = GlobalKey<FormState>();
    
    final patientIdController = TextEditingController(text: record?['patient_id']?.toString() ?? '');
    final descriptionController = TextEditingController(text: record?['description'] ?? '');
    final totalAmountController = TextEditingController(text: record?['total_amount']?.toString() ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Billing Record' : 'New Billing Record'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: patientIdController,
                  decoration: const InputDecoration(labelText: 'Patient ID'),
                  keyboardType: TextInputType.number,
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: descriptionController,
                  decoration: const InputDecoration(labelText: 'Description'),
                  maxLines: 2,
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: totalAmountController,
                  decoration: const InputDecoration(labelText: 'Total Amount'),
                  keyboardType: TextInputType.number,
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _saveBilling(
              formKey,
              {
                'patient_id': int.tryParse(patientIdController.text),
                'description': descriptionController.text,
                'total_amount': double.tryParse(totalAmountController.text),
              },
              isEditing ? record['id'] : null,
            ),
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveBilling(GlobalKey<FormState> formKey, Map<String, dynamic> data, int? recordId) async {
    if (!formKey.currentState!.validate()) return;

    try {
      if (recordId != null) {
        await ApiService.updateBilling(recordId, data);
      } else {
        await ApiService.createBilling(data);
      }
      
      Navigator.pop(context);
      _loadBillingRecords();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(recordId != null ? 'Billing record updated successfully' : 'Billing record created successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _markAsPaid(int recordId) async {
    try {
      await ApiService.updateBilling(recordId, {'payment_status': 'paid'});
      _loadBillingRecords();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Billing record marked as paid'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _confirmDeleteBilling(Map<String, dynamic> record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this billing record?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _deleteBilling(record['id']),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteBilling(int recordId) async {
    try {
      await ApiService.deleteBilling(recordId);
      Navigator.pop(context);
      _loadBillingRecords();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Billing record deleted successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
