const Doctor = require('../models/Doctor');
const Joi = require('joi');

const doctorSchema = Joi.object({
  first_name: Joi.string().min(1).max(50).required(),
  last_name: Joi.string().min(1).max(50).required(),
  email: Joi.string().email().allow(null, ''),
  phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).allow(null, ''),
  license_number: Joi.string().min(1).max(50).required(),
  specialization: Joi.string().min(1).max(100).required(),
  department_id: Joi.number().integer().positive().allow(null),
  hire_date: Joi.date().iso().required(),
  salary: Joi.number().positive().allow(null),
  experience_years: Joi.number().integer().min(0).allow(null),
  qualification: Joi.string().max(200).allow(null, ''),
  address: Joi.string().max(500).allow(null, ''),
  date_of_birth: Joi.date().iso().allow(null),
  gender: Joi.string().valid('Male', 'Female', 'Other').allow(null),
  status: Joi.string().valid('active', 'inactive', 'on_leave').default('active')
});

const updateDoctorSchema = doctorSchema.fork(
  ['first_name', 'last_name', 'license_number', 'specialization', 'hire_date'], 
  (schema) => schema.optional()
);

class DoctorController {
  static async getAllDoctors(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        status, 
        specialization, 
        department_id, 
        search 
      } = req.query;

      const offset = (page - 1) * limit;
      const filters = {};

      if (status) filters.status = status;
      if (specialization) filters.specialization = specialization;
      if (department_id) filters.department_id = department_id;
      if (search) filters.search = search;

      const doctors = await Doctor.findAll(parseInt(limit), offset, filters);

      res.json({
        success: true,
        data: doctors,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: doctors.length
        }
      });
    } catch (error) {
      console.error('Error fetching doctors:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch doctors',
        message: error.message
      });
    }
  }

  static async getDoctorById(req, res) {
    try {
      const { id } = req.params;
      const doctor = await Doctor.findById(id);

      if (!doctor) {
        return res.status(404).json({
          success: false,
          error: 'Doctor not found'
        });
      }

      res.json({
        success: true,
        data: doctor
      });
    } catch (error) {
      console.error('Error fetching doctor:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch doctor',
        message: error.message
      });
    }
  }

  static async createDoctor(req, res) {
    try {
      const { error, value } = doctorSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const doctor = await Doctor.create(value);

      res.status(201).json({
        success: true,
        data: doctor,
        message: 'Doctor created successfully'
      });
    } catch (error) {
      console.error('Error creating doctor:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create doctor',
        message: error.message
      });
    }
  }

  static async updateDoctor(req, res) {
    try {
      const { id } = req.params;
      const { error, value } = updateDoctorSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const doctor = await Doctor.update(id, value);

      if (!doctor) {
        return res.status(404).json({
          success: false,
          error: 'Doctor not found'
        });
      }

      res.json({
        success: true,
        data: doctor,
        message: 'Doctor updated successfully'
      });
    } catch (error) {
      console.error('Error updating doctor:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update doctor',
        message: error.message
      });
    }
  }

  static async deleteDoctor(req, res) {
    try {
      const { id } = req.params;
      const deleted = await Doctor.delete(id);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: 'Doctor not found'
        });
      }

      res.json({
        success: true,
        message: 'Doctor deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting doctor:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete doctor',
        message: error.message
      });
    }
  }

  static async getDoctorStatistics(req, res) {
    try {
      const statistics = await Doctor.getStatistics();
      const topPerforming = await Doctor.getTopPerformingDoctors();
      const bySpecialization = await Doctor.getDoctorsBySpecialization();

      res.json({
        success: true,
        data: {
          overview: statistics,
          topPerforming: topPerforming,
          bySpecialization: bySpecialization
        }
      });
    } catch (error) {
      console.error('Error fetching doctor statistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch doctor statistics',
        message: error.message
      });
    }
  }

  static async getAvailableDoctors(req, res) {
    try {
      const { date, time } = req.query;

      if (!date || !time) {
        return res.status(400).json({
          success: false,
          error: 'Date and time are required'
        });
      }

      const doctors = await Doctor.getAvailableDoctors(date, time);

      res.json({
        success: true,
        data: doctors
      });
    } catch (error) {
      console.error('Error fetching available doctors:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch available doctors',
        message: error.message
      });
    }
  }

  static async getDoctorAppointments(req, res) {
    try {
      const { id } = req.params;
      const { limit = 10, status } = req.query;

      const doctor = await Doctor.findById(id);
      if (!doctor) {
        return res.status(404).json({
          success: false,
          error: 'Doctor not found'
        });
      }

      const appointments = await doctor.getAppointments(parseInt(limit), status);

      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Error fetching doctor appointments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch doctor appointments',
        message: error.message
      });
    }
  }

  static async getDoctorUpcomingAppointments(req, res) {
    try {
      const { id } = req.params;

      const doctor = await Doctor.findById(id);
      if (!doctor) {
        return res.status(404).json({
          success: false,
          error: 'Doctor not found'
        });
      }

      const appointments = await doctor.getUpcomingAppointments();

      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Error fetching doctor upcoming appointments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch doctor upcoming appointments',
        message: error.message
      });
    }
  }

  static async getDoctorWorkload(req, res) {
    try {
      const { id } = req.params;
      const { start_date, end_date } = req.query;

      if (!start_date || !end_date) {
        return res.status(400).json({
          success: false,
          error: 'Start date and end date are required'
        });
      }

      const doctor = await Doctor.findById(id);
      if (!doctor) {
        return res.status(404).json({
          success: false,
          error: 'Doctor not found'
        });
      }

      const workload = await doctor.getWorkload(start_date, end_date);

      res.json({
        success: true,
        data: workload
      });
    } catch (error) {
      console.error('Error fetching doctor workload:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch doctor workload',
        message: error.message
      });
    }
  }
}

module.exports = DoctorController;
