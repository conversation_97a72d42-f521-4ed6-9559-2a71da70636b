const bcrypt = require('bcryptjs');
const database = require('./db/database');

async function createAdminUser() {
  try {
    await database.init();
    
    // Check if admin user already exists
    const existingAdmin = await database.get(
      'SELECT id FROM users WHERE username = ?',
      ['admin']
    );
    
    if (existingAdmin) {
      console.log('Admin user already exists!');
      return;
    }
    
    // Hash the password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash('admin123', saltRounds);
    
    // Create admin user
    await database.run(`
      INSERT INTO users (username, email, password_hash, first_name, last_name, role, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'admin',
      '<EMAIL>',
      passwordHash,
      'Admin',
      'User',
      'admin',
      1
    ]);
    
    console.log('✅ Admin user created successfully!');
    console.log('📧 Username: admin');
    console.log('🔑 Password: admin123');
    console.log('👤 Role: admin');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    database.close();
    process.exit(0);
  }
}

if (require.main === module) {
  createAdminUser();
}

module.exports = createAdminUser;
