const database = require('../db/database');

class Patient {
  constructor(data) {
    Object.assign(this, data);
  }

  static async findAll(limit = 100, offset = 0, filters = {}) {
    let query = `
      SELECT p.*, 
             COUNT(a.id) as appointment_count,
             MAX(a.appointment_date) as last_appointment_date
      FROM patients p
      LEFT JOIN appointments a ON p.id = a.patient_id
    `;
    
    const conditions = [];
    const params = [];

    if (filters.status) {
      conditions.push('p.status = ?');
      params.push(filters.status);
    }

    if (filters.search) {
      conditions.push('(p.first_name LIKE ? OR p.last_name LIKE ? OR p.email LIKE ?)');
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    if (filters.blood_type) {
      conditions.push('p.blood_type = ?');
      params.push(filters.blood_type);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += `
      GROUP BY p.id
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    
    const rows = await database.query(query, params);
    return rows.map(row => new Patient(row));
  }

  static async findById(id) {
    const query = `
      SELECT p.*,
             COUNT(a.id) as appointment_count,
             MAX(a.appointment_date) as last_appointment_date,
             SUM(CASE WHEN b.payment_status = 'pending' THEN b.balance_amount ELSE 0 END) as outstanding_balance
      FROM patients p
      LEFT JOIN appointments a ON p.id = a.patient_id
      LEFT JOIN billing b ON p.id = b.patient_id
      WHERE p.id = ?
      GROUP BY p.id
    `;
    
    const row = await database.get(query, [id]);
    return row ? new Patient(row) : null;
  }

  static async create(patientData) {
    const {
      first_name, last_name, email, phone, date_of_birth, gender,
      address, emergency_contact_name, emergency_contact_phone,
      blood_type, allergies, medical_history, insurance_number,
      insurance_provider, status = 'active'
    } = patientData;

    const query = `
      INSERT INTO patients (
        first_name, last_name, email, phone, date_of_birth, gender,
        address, emergency_contact_name, emergency_contact_phone,
        blood_type, allergies, medical_history, insurance_number,
        insurance_provider, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.run(query, [
      first_name, last_name, email, phone, date_of_birth, gender,
      address, emergency_contact_name, emergency_contact_phone,
      blood_type, allergies, medical_history, insurance_number,
      insurance_provider, status
    ]);

    return this.findById(result.id);
  }

  static async update(id, patientData) {
    const fields = [];
    const params = [];

    Object.keys(patientData).forEach(key => {
      if (patientData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(patientData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `UPDATE patients SET ${fields.join(', ')} WHERE id = ?`;
    await database.run(query, params);

    return this.findById(id);
  }

  static async delete(id) {
    const result = await database.run('DELETE FROM patients WHERE id = ?', [id]);
    return result.changes > 0;
  }

  static async getStatistics() {
    const stats = await database.get(`
      SELECT 
        COUNT(*) as total_patients,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_patients,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_patients,
        COUNT(CASE WHEN gender = 'Male' THEN 1 END) as male_patients,
        COUNT(CASE WHEN gender = 'Female' THEN 1 END) as female_patients,
        AVG(CASE WHEN date_of_birth IS NOT NULL 
            THEN (julianday('now') - julianday(date_of_birth)) / 365.25 
            END) as average_age
      FROM patients
    `);

    return stats;
  }

  static async getPatientsByAgeGroup() {
    const query = `
      SELECT 
        CASE 
          WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 < 18 THEN 'Under 18'
          WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 18 AND 30 THEN '18-30'
          WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 31 AND 50 THEN '31-50'
          WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 51 AND 70 THEN '51-70'
          ELSE 'Over 70'
        END as age_group,
        COUNT(*) as count
      FROM patients 
      WHERE date_of_birth IS NOT NULL
      GROUP BY age_group
      ORDER BY age_group
    `;

    return database.query(query);
  }

  // Instance methods
  async getAppointments(limit = 10) {
    const query = `
      SELECT a.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization, dept.name as department_name
      FROM appointments a
      JOIN doctors d ON a.doctor_id = d.id
      LEFT JOIN departments dept ON d.department_id = dept.id
      WHERE a.patient_id = ?
      ORDER BY a.appointment_date DESC, a.appointment_time DESC
      LIMIT ?
    `;

    return database.query(query, [this.id, limit]);
  }

  async getPrescriptions(limit = 10) {
    const query = `
      SELECT pr.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name
      FROM prescriptions pr
      JOIN doctors d ON pr.doctor_id = d.id
      WHERE pr.patient_id = ?
      ORDER BY pr.prescribed_date DESC
      LIMIT ?
    `;

    return database.query(query, [this.id, limit]);
  }

  async getBillingHistory(limit = 10) {
    const query = `
      SELECT b.*, a.appointment_date, a.reason as appointment_reason
      FROM billing b
      LEFT JOIN appointments a ON b.appointment_id = a.id
      WHERE b.patient_id = ?
      ORDER BY b.billing_date DESC
      LIMIT ?
    `;

    return database.query(query, [this.id, limit]);
  }
}

module.exports = Patient;
