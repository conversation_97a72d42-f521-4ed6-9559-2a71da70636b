const database = require('../db/database');

class Doctor {
  constructor(data) {
    Object.assign(this, data);
  }

  static async findAll(limit = 100, offset = 0, filters = {}) {
    let query = `
      SELECT d.*, 
             dept.name as department_name,
             COUNT(a.id) as appointment_count,
             AVG(CASE WHEN a.status = 'completed' THEN 1.0 ELSE 0.0 END) as completion_rate
      FROM doctors d
      LEFT JOIN departments dept ON d.department_id = dept.id
      LEFT JOIN appointments a ON d.id = a.doctor_id
    `;
    
    const conditions = [];
    const params = [];

    if (filters.status) {
      conditions.push('d.status = ?');
      params.push(filters.status);
    }

    if (filters.specialization) {
      conditions.push('d.specialization = ?');
      params.push(filters.specialization);
    }

    if (filters.department_id) {
      conditions.push('d.department_id = ?');
      params.push(filters.department_id);
    }

    if (filters.search) {
      conditions.push('(d.first_name LIKE ? OR d.last_name LIKE ? OR d.email LIKE ?)');
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += `
      GROUP BY d.id
      ORDER BY d.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    
    const rows = await database.query(query, params);
    return rows.map(row => new Doctor(row));
  }

  static async findById(id) {
    const query = `
      SELECT d.*,
             dept.name as department_name,
             COUNT(a.id) as total_appointments,
             COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_appointments,
             COUNT(CASE WHEN a.appointment_date >= date('now', '-30 days') THEN 1 END) as recent_appointments
      FROM doctors d
      LEFT JOIN departments dept ON d.department_id = dept.id
      LEFT JOIN appointments a ON d.id = a.doctor_id
      WHERE d.id = ?
      GROUP BY d.id
    `;
    
    const row = await database.get(query, [id]);
    return row ? new Doctor(row) : null;
  }

  static async create(doctorData) {
    const {
      first_name, last_name, email, phone, license_number, specialization,
      department_id, hire_date, salary, experience_years, qualification,
      address, date_of_birth, gender, status = 'active'
    } = doctorData;

    const query = `
      INSERT INTO doctors (
        first_name, last_name, email, phone, license_number, specialization,
        department_id, hire_date, salary, experience_years, qualification,
        address, date_of_birth, gender, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.run(query, [
      first_name, last_name, email, phone, license_number, specialization,
      department_id, hire_date, salary, experience_years, qualification,
      address, date_of_birth, gender, status
    ]);

    return this.findById(result.id);
  }

  static async update(id, doctorData) {
    const fields = [];
    const params = [];

    Object.keys(doctorData).forEach(key => {
      if (doctorData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(doctorData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `UPDATE doctors SET ${fields.join(', ')} WHERE id = ?`;
    await database.run(query, params);

    return this.findById(id);
  }

  static async delete(id) {
    const result = await database.run('DELETE FROM doctors WHERE id = ?', [id]);
    return result.changes > 0;
  }

  static async getStatistics() {
    const stats = await database.get(`
      SELECT 
        COUNT(*) as total_doctors,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_doctors,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_doctors,
        COUNT(CASE WHEN status = 'on_leave' THEN 1 END) as doctors_on_leave,
        AVG(salary) as average_salary,
        AVG(experience_years) as average_experience
      FROM doctors
    `);

    return stats;
  }

  static async getTopPerformingDoctors(limit = 10) {
    const query = `
      SELECT d.id, d.first_name, d.last_name, d.specialization,
             dept.name as department_name,
             COUNT(a.id) as total_appointments,
             COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_appointments,
             ROUND(COUNT(CASE WHEN a.status = 'completed' THEN 1 END) * 100.0 / COUNT(a.id), 2) as completion_rate
      FROM doctors d
      LEFT JOIN departments dept ON d.department_id = dept.id
      LEFT JOIN appointments a ON d.id = a.doctor_id
      WHERE d.status = 'active'
      GROUP BY d.id
      HAVING COUNT(a.id) > 0
      ORDER BY completion_rate DESC, total_appointments DESC
      LIMIT ?
    `;

    return database.query(query, [limit]);
  }

  static async getDoctorsBySpecialization() {
    const query = `
      SELECT specialization, COUNT(*) as count
      FROM doctors 
      WHERE status = 'active'
      GROUP BY specialization
      ORDER BY count DESC
    `;

    return database.query(query);
  }

  static async getAvailableDoctors(date, time) {
    const query = `
      SELECT d.*, dept.name as department_name
      FROM doctors d
      LEFT JOIN departments dept ON d.department_id = dept.id
      WHERE d.status = 'active'
      AND d.id NOT IN (
        SELECT doctor_id 
        FROM appointments 
        WHERE appointment_date = ? 
        AND appointment_time = ?
        AND status NOT IN ('cancelled', 'no_show')
      )
      ORDER BY d.specialization, d.last_name
    `;

    const rows = await database.query(query, [date, time]);
    return rows.map(row => new Doctor(row));
  }

  // Instance methods
  async getAppointments(limit = 10, status = null) {
    let query = `
      SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.email as patient_email
      FROM appointments a
      JOIN patients p ON a.patient_id = p.id
      WHERE a.doctor_id = ?
    `;

    const params = [this.id];

    if (status) {
      query += ' AND a.status = ?';
      params.push(status);
    }

    query += `
      ORDER BY a.appointment_date DESC, a.appointment_time DESC
      LIMIT ?
    `;
    params.push(limit);

    return database.query(query, params);
  }

  async getUpcomingAppointments() {
    const query = `
      SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone
      FROM appointments a
      JOIN patients p ON a.patient_id = p.id
      WHERE a.doctor_id = ? 
      AND a.appointment_date >= date('now')
      AND a.status IN ('scheduled', 'confirmed')
      ORDER BY a.appointment_date ASC, a.appointment_time ASC
      LIMIT 20
    `;

    return database.query(query, [this.id]);
  }

  async getPrescriptions(limit = 10) {
    const query = `
      SELECT pr.*, p.first_name as patient_first_name, p.last_name as patient_last_name
      FROM prescriptions pr
      JOIN patients p ON pr.patient_id = p.id
      WHERE pr.doctor_id = ?
      ORDER BY pr.prescribed_date DESC
      LIMIT ?
    `;

    return database.query(query, [this.id, limit]);
  }

  async getWorkload(startDate, endDate) {
    const query = `
      SELECT 
        COUNT(*) as total_appointments,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_appointments,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_appointments,
        COUNT(CASE WHEN status = 'no_show' THEN 1 END) as no_show_appointments
      FROM appointments
      WHERE doctor_id = ?
      AND appointment_date BETWEEN ? AND ?
    `;

    return database.get(query, [this.id, startDate, endDate]);
  }
}

module.exports = Doctor;
