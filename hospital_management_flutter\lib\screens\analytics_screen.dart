import 'package:flutter/material.dart';
import '../services/api_service.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  Map<String, dynamic>? patientDemographics;
  Map<String, dynamic>? doctorPerformance;
  Map<String, dynamic>? financialSummary;
  Map<String, dynamic>? appointmentTrends;
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final results = await Future.wait([
        ApiService.getPatientDemographics(),
        ApiService.getDoctorPerformance(),
        ApiService.getFinancialSummary(),
        ApiService.getAppointmentTrends(),
      ]);

      setState(() {
        patientDemographics = results[0]['data'];
        doctorPerformance = results[1]['data'];
        financialSummary = results[2]['data'];
        appointmentTrends = results[3]['data'];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics & Reports'),
        backgroundColor: Colors.teal[800],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAnalytics,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, size: 64, color: Colors.red[400]),
                      const SizedBox(height: 16),
                      Text('Error: $error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadAnalytics,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _buildAnalytics(),
    );
  }

  Widget _buildAnalytics() {
    return RefreshIndicator(
      onRefresh: _loadAnalytics,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFinancialSummary(),
            const SizedBox(height: 24),
            _buildPatientDemographics(),
            const SizedBox(height: 24),
            _buildDoctorPerformance(),
            const SizedBox(height: 24),
            _buildAppointmentTrends(),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummary() {
    if (financialSummary == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Financial Summary',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildFinancialCard(
              'Total Revenue',
              '\$${financialSummary!['total_revenue'] ?? 0}',
              Icons.attach_money,
              Colors.green,
            ),
            _buildFinancialCard(
              'Outstanding',
              '\$${financialSummary!['outstanding_amount'] ?? 0}',
              Icons.pending,
              Colors.orange,
            ),
            _buildFinancialCard(
              'This Month',
              '\$${financialSummary!['monthly_revenue'] ?? 0}',
              Icons.calendar_month,
              Colors.blue,
            ),
            _buildFinancialCard(
              'Paid Bills',
              '${financialSummary!['paid_bills'] ?? 0}',
              Icons.check_circle,
              Colors.teal,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinancialCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPatientDemographics() {
    if (patientDemographics == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Patient Demographics',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildDemographicRow('Total Patients', patientDemographics!['total_patients']?.toString() ?? '0'),
                _buildDemographicRow('Male Patients', patientDemographics!['male_patients']?.toString() ?? '0'),
                _buildDemographicRow('Female Patients', patientDemographics!['female_patients']?.toString() ?? '0'),
                _buildDemographicRow('Average Age', patientDemographics!['average_age']?.toString() ?? '0'),
                _buildDemographicRow('New This Month', patientDemographics!['new_this_month']?.toString() ?? '0'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDemographicRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildDoctorPerformance() {
    if (doctorPerformance == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Doctor Performance',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildDemographicRow('Total Doctors', doctorPerformance!['total_doctors']?.toString() ?? '0'),
                _buildDemographicRow('Active Doctors', doctorPerformance!['active_doctors']?.toString() ?? '0'),
                _buildDemographicRow('Avg Experience', '${doctorPerformance!['average_experience'] ?? 0} years'),
                _buildDemographicRow('Specializations', doctorPerformance!['total_specializations']?.toString() ?? '0'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAppointmentTrends() {
    if (appointmentTrends == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Appointment Trends',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildDemographicRow('Total Appointments', appointmentTrends!['total_appointments']?.toString() ?? '0'),
                _buildDemographicRow('Completed', appointmentTrends!['completed_appointments']?.toString() ?? '0'),
                _buildDemographicRow('Cancelled', appointmentTrends!['cancelled_appointments']?.toString() ?? '0'),
                _buildDemographicRow('This Week', appointmentTrends!['this_week']?.toString() ?? '0'),
                _buildDemographicRow('This Month', appointmentTrends!['this_month']?.toString() ?? '0'),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
