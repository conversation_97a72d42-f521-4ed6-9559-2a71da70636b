import 'package:flutter/material.dart';
import '../services/api_service.dart';

class DoctorsScreen extends StatefulWidget {
  const DoctorsScreen({super.key});

  @override
  State<DoctorsScreen> createState() => _DoctorsScreenState();
}

class _DoctorsScreenState extends State<DoctorsScreen> {
  List<dynamic> doctors = [];
  bool isLoading = true;
  String? error;
  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadDoctors();
  }

  Future<void> _loadDoctors() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final response = await ApiService.getDoctors();
      setState(() {
        doctors = response['data'] ?? [];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  List<dynamic> get filteredDoctors {
    if (searchQuery.isEmpty) return doctors;
    return doctors.where((doctor) {
      final name = '${doctor['first_name']} ${doctor['last_name']}'.toLowerCase();
      final specialization = (doctor['specialization'] ?? '').toLowerCase();
      final email = (doctor['email'] ?? '').toLowerCase();
      final query = searchQuery.toLowerCase();
      return name.contains(query) || specialization.contains(query) || email.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Doctors'),
        backgroundColor: Colors.green[800],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDoctors,
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search doctors...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, size: 64, color: Colors.red[400]),
                            const SizedBox(height: 16),
                            Text('Error: $error'),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadDoctors,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _buildDoctorsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddDoctorDialog(),
        backgroundColor: Colors.green[800],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildDoctorsList() {
    final displayDoctors = filteredDoctors;
    
    if (displayDoctors.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.medical_services_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No doctors found'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDoctors,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: displayDoctors.length,
        itemBuilder: (context, index) {
          final doctor = displayDoctors[index];
          return _buildDoctorCard(doctor);
        },
      ),
    );
  }

  Widget _buildDoctorCard(Map<String, dynamic> doctor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.green[100],
          child: Text(
            '${doctor['first_name']?[0] ?? ''}${doctor['last_name']?[0] ?? ''}'.toUpperCase(),
            style: TextStyle(color: Colors.green[800], fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          'Dr. ${doctor['first_name'] ?? ''} ${doctor['last_name'] ?? ''}',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (doctor['specialization'] != null) 
              Text('🩺 ${doctor['specialization']}'),
            if (doctor['email'] != null) Text('📧 ${doctor['email']}'),
            if (doctor['phone'] != null) Text('📞 ${doctor['phone']}'),
            if (doctor['experience_years'] != null) 
              Text('📅 ${doctor['experience_years']} years experience'),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility),
                  SizedBox(width: 8),
                  Text('View Details'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          onSelected: (value) => _handleDoctorAction(value, doctor),
        ),
        onTap: () => _showDoctorDetails(doctor),
      ),
    );
  }

  void _handleDoctorAction(String action, Map<String, dynamic> doctor) {
    switch (action) {
      case 'view':
        _showDoctorDetails(doctor);
        break;
      case 'edit':
        _showEditDoctorDialog(doctor);
        break;
      case 'delete':
        _confirmDeleteDoctor(doctor);
        break;
    }
  }

  void _showDoctorDetails(Map<String, dynamic> doctor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Dr. ${doctor['first_name']} ${doctor['last_name']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Specialization', doctor['specialization']),
              _buildDetailRow('License Number', doctor['license_number']),
              _buildDetailRow('Email', doctor['email']),
              _buildDetailRow('Phone', doctor['phone']),
              _buildDetailRow('Experience', '${doctor['experience_years']} years'),
              _buildDetailRow('Qualification', doctor['qualification']),
              _buildDetailRow('Hire Date', doctor['hire_date']),
              _buildDetailRow('Salary', doctor['salary']?.toString()),
              _buildDetailRow('Address', doctor['address']),
              _buildDetailRow('Status', doctor['status']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, dynamic value) {
    if (value == null || value.toString().isEmpty) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value.toString())),
        ],
      ),
    );
  }

  void _showAddDoctorDialog() {
    _showDoctorForm();
  }

  void _showEditDoctorDialog(Map<String, dynamic> doctor) {
    _showDoctorForm(doctor: doctor);
  }

  void _showDoctorForm({Map<String, dynamic>? doctor}) {
    final isEditing = doctor != null;
    final formKey = GlobalKey<FormState>();

    final firstNameController = TextEditingController(text: doctor?['first_name'] ?? '');
    final lastNameController = TextEditingController(text: doctor?['last_name'] ?? '');
    final emailController = TextEditingController(text: doctor?['email'] ?? '');
    final phoneController = TextEditingController(text: doctor?['phone'] ?? '');
    final specializationController = TextEditingController(text: doctor?['specialization'] ?? '');
    final licenseController = TextEditingController(text: doctor?['license_number'] ?? '');
    final experienceController = TextEditingController(text: doctor?['experience_years']?.toString() ?? '');
    final hireDateController = TextEditingController(text: doctor?['hire_date'] ?? '');
    final salaryController = TextEditingController(text: doctor?['salary']?.toString() ?? '');
    final qualificationController = TextEditingController(text: doctor?['qualification'] ?? '');
    final addressController = TextEditingController(text: doctor?['address'] ?? '');
    final dateOfBirthController = TextEditingController(text: doctor?['date_of_birth'] ?? '');

    String? selectedGender = doctor?['gender'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Doctor' : 'Add New Doctor'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: firstNameController,
                  decoration: const InputDecoration(labelText: 'First Name *'),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: lastNameController,
                  decoration: const InputDecoration(labelText: 'Last Name *'),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: emailController,
                  decoration: const InputDecoration(labelText: 'Email'),
                  keyboardType: TextInputType.emailAddress,
                ),
                TextFormField(
                  controller: phoneController,
                  decoration: const InputDecoration(labelText: 'Phone'),
                  keyboardType: TextInputType.phone,
                ),
                TextFormField(
                  controller: specializationController,
                  decoration: const InputDecoration(labelText: 'Specialization *'),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: licenseController,
                  decoration: const InputDecoration(labelText: 'License Number *'),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: hireDateController,
                  decoration: const InputDecoration(
                    labelText: 'Hire Date * (YYYY-MM-DD)',
                    hintText: '2024-01-15',
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: experienceController,
                  decoration: const InputDecoration(labelText: 'Experience (years)'),
                  keyboardType: TextInputType.number,
                ),
                TextFormField(
                  controller: salaryController,
                  decoration: const InputDecoration(labelText: 'Salary'),
                  keyboardType: TextInputType.number,
                ),
                TextFormField(
                  controller: qualificationController,
                  decoration: const InputDecoration(labelText: 'Qualification'),
                ),
                TextFormField(
                  controller: addressController,
                  decoration: const InputDecoration(labelText: 'Address'),
                  maxLines: 2,
                ),
                TextFormField(
                  controller: dateOfBirthController,
                  decoration: const InputDecoration(
                    labelText: 'Date of Birth (YYYY-MM-DD)',
                    hintText: '1980-01-15',
                  ),
                ),
                DropdownButtonFormField<String>(
                  value: selectedGender,
                  decoration: const InputDecoration(labelText: 'Gender'),
                  items: ['Male', 'Female', 'Other'].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    selectedGender = newValue;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _saveDoctor(
              formKey,
              {
                'first_name': firstNameController.text,
                'last_name': lastNameController.text,
                'email': emailController.text.isEmpty ? null : emailController.text,
                'phone': phoneController.text.isEmpty ? null : phoneController.text,
                'specialization': specializationController.text,
                'license_number': licenseController.text,
                'hire_date': hireDateController.text,
                'experience_years': experienceController.text.isEmpty ? null : int.tryParse(experienceController.text),
                'salary': salaryController.text.isEmpty ? null : double.tryParse(salaryController.text),
                'qualification': qualificationController.text.isEmpty ? null : qualificationController.text,
                'address': addressController.text.isEmpty ? null : addressController.text,
                'date_of_birth': dateOfBirthController.text.isEmpty ? null : dateOfBirthController.text,
                'gender': selectedGender,
              },
              isEditing ? doctor['id'] : null,
            ),
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveDoctor(GlobalKey<FormState> formKey, Map<String, dynamic> data, int? doctorId) async {
    if (!formKey.currentState!.validate()) return;

    try {
      if (doctorId != null) {
        await ApiService.updateDoctor(doctorId, data);
      } else {
        await ApiService.createDoctor(data);
      }
      
      if (mounted) {
        Navigator.pop(context);
        _loadDoctors();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(doctorId != null ? 'Doctor updated successfully' : 'Doctor added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _confirmDeleteDoctor(Map<String, dynamic> doctor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete Dr. ${doctor['first_name']} ${doctor['last_name']}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _deleteDoctor(doctor['id']),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteDoctor(int doctorId) async {
    try {
      await ApiService.deleteDoctor(doctorId);
      if (mounted) {
        Navigator.pop(context);
        _loadDoctors();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Doctor deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
