const database = require('../db/database');

class Appointment {
  constructor(data) {
    Object.assign(this, data);
  }

  static async findAll(limit = 100, offset = 0, filters = {}) {
    let query = `
      SELECT a.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.email as patient_email,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization, dept.name as department_name
      FROM appointments a
      JOIN patients p ON a.patient_id = p.id
      JOIN doctors d ON a.doctor_id = d.id
      LEFT JOIN departments dept ON d.department_id = dept.id
    `;
    
    const conditions = [];
    const params = [];

    if (filters.status) {
      conditions.push('a.status = ?');
      params.push(filters.status);
    }

    if (filters.doctor_id) {
      conditions.push('a.doctor_id = ?');
      params.push(filters.doctor_id);
    }

    if (filters.patient_id) {
      conditions.push('a.patient_id = ?');
      params.push(filters.patient_id);
    }

    if (filters.date_from) {
      conditions.push('a.appointment_date >= ?');
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      conditions.push('a.appointment_date <= ?');
      params.push(filters.date_to);
    }

    if (filters.appointment_type) {
      conditions.push('a.appointment_type = ?');
      params.push(filters.appointment_type);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += `
      ORDER BY a.appointment_date DESC, a.appointment_time DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    
    const rows = await database.query(query, params);
    return rows.map(row => new Appointment(row));
  }

  static async findById(id) {
    const query = `
      SELECT a.*,
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.email as patient_email, p.date_of_birth,
             p.blood_type, p.allergies, p.medical_history,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization, d.phone as doctor_phone, d.email as doctor_email,
             dept.name as department_name
      FROM appointments a
      JOIN patients p ON a.patient_id = p.id
      JOIN doctors d ON a.doctor_id = d.id
      LEFT JOIN departments dept ON d.department_id = dept.id
      WHERE a.id = ?
    `;
    
    const row = await database.get(query, [id]);
    return row ? new Appointment(row) : null;
  }

  static async create(appointmentData) {
    const {
      patient_id, doctor_id, appointment_date, appointment_time,
      duration_minutes = 30, reason, notes, status = 'scheduled',
      appointment_type = 'consultation', priority = 'normal'
    } = appointmentData;

    // Check for conflicts
    const conflict = await database.get(`
      SELECT id FROM appointments 
      WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ?
      AND status NOT IN ('cancelled', 'no_show')
    `, [doctor_id, appointment_date, appointment_time]);

    if (conflict) {
      throw new Error('Doctor is not available at this time slot');
    }

    const query = `
      INSERT INTO appointments (
        patient_id, doctor_id, appointment_date, appointment_time,
        duration_minutes, reason, notes, status, appointment_type, priority
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.run(query, [
      patient_id, doctor_id, appointment_date, appointment_time,
      duration_minutes, reason, notes, status, appointment_type, priority
    ]);

    return this.findById(result.id);
  }

  static async update(id, appointmentData) {
    const fields = [];
    const params = [];

    // If updating doctor, date, or time, check for conflicts
    if (appointmentData.doctor_id || appointmentData.appointment_date || appointmentData.appointment_time) {
      const current = await this.findById(id);
      const doctor_id = appointmentData.doctor_id || current.doctor_id;
      const appointment_date = appointmentData.appointment_date || current.appointment_date;
      const appointment_time = appointmentData.appointment_time || current.appointment_time;

      const conflict = await database.get(`
        SELECT id FROM appointments 
        WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ?
        AND id != ? AND status NOT IN ('cancelled', 'no_show')
      `, [doctor_id, appointment_date, appointment_time, id]);

      if (conflict) {
        throw new Error('Doctor is not available at this time slot');
      }
    }

    Object.keys(appointmentData).forEach(key => {
      if (appointmentData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(appointmentData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `UPDATE appointments SET ${fields.join(', ')} WHERE id = ?`;
    await database.run(query, params);

    return this.findById(id);
  }

  static async delete(id) {
    const result = await database.run('DELETE FROM appointments WHERE id = ?', [id]);
    return result.changes > 0;
  }

  static async bookAppointment(appointmentData) {
    return database.transaction(async (db) => {
      // Create the appointment
      const appointment = await this.create(appointmentData);

      // Create billing record if needed
      if (appointmentData.create_billing) {
        const billingData = {
          patient_id: appointmentData.patient_id,
          appointment_id: appointment.id,
          invoice_number: `INV${Date.now()}${Math.floor(Math.random() * 1000)}`,
          total_amount: appointmentData.consultation_fee || 100,
          billing_date: new Date().toISOString().split('T')[0],
          due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          description: 'Consultation fee'
        };

        await db.run(`
          INSERT INTO billing (patient_id, appointment_id, invoice_number, total_amount, 
                              billing_date, due_date, description)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          billingData.patient_id, billingData.appointment_id, billingData.invoice_number,
          billingData.total_amount, billingData.billing_date, billingData.due_date,
          billingData.description
        ]);
      }

      return appointment;
    });
  }

  static async getStatistics() {
    const stats = await database.get(`
      SELECT 
        COUNT(*) as total_appointments,
        COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled_appointments,
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_appointments,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_appointments,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_appointments,
        COUNT(CASE WHEN status = 'no_show' THEN 1 END) as no_show_appointments,
        COUNT(CASE WHEN appointment_date = date('now') THEN 1 END) as today_appointments,
        COUNT(CASE WHEN appointment_date > date('now') THEN 1 END) as future_appointments
      FROM appointments
    `);

    return stats;
  }

  static async getAppointmentsByDate(startDate, endDate) {
    const query = `
      SELECT appointment_date, COUNT(*) as count,
             COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count
      FROM appointments
      WHERE appointment_date BETWEEN ? AND ?
      GROUP BY appointment_date
      ORDER BY appointment_date
    `;

    return database.query(query, [startDate, endDate]);
  }

  static async getAppointmentsByType() {
    const query = `
      SELECT appointment_type, COUNT(*) as count
      FROM appointments
      GROUP BY appointment_type
      ORDER BY count DESC
    `;

    return database.query(query);
  }

  static async getTodaysAppointments() {
    const query = `
      SELECT a.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization
      FROM appointments a
      JOIN patients p ON a.patient_id = p.id
      JOIN doctors d ON a.doctor_id = d.id
      WHERE a.appointment_date = date('now')
      AND a.status IN ('scheduled', 'confirmed')
      ORDER BY a.appointment_time ASC
    `;

    const rows = await database.query(query);
    return rows.map(row => new Appointment(row));
  }

  static async getUpcomingAppointments(days = 7) {
    const query = `
      SELECT a.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization
      FROM appointments a
      JOIN patients p ON a.patient_id = p.id
      JOIN doctors d ON a.doctor_id = d.id
      WHERE a.appointment_date BETWEEN date('now') AND date('now', '+${days} days')
      AND a.status IN ('scheduled', 'confirmed')
      ORDER BY a.appointment_date ASC, a.appointment_time ASC
    `;

    const rows = await database.query(query);
    return rows.map(row => new Appointment(row));
  }

  // Instance methods
  async getPrescriptions() {
    const query = `
      SELECT pr.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name
      FROM prescriptions pr
      JOIN doctors d ON pr.doctor_id = d.id
      WHERE pr.appointment_id = ?
      ORDER BY pr.prescribed_date DESC
    `;

    return database.query(query, [this.id]);
  }

  async getBilling() {
    const query = `
      SELECT * FROM billing WHERE appointment_id = ?
    `;

    return database.get(query, [this.id]);
  }
}

module.exports = Appointment;
