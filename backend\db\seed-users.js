const bcrypt = require('bcryptjs');
const database = require('./database');

async function seedUsers() {
  console.log('Seeding users...');

  const users = [
    {
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      first_name: '<PERSON>',
      last_name: 'Administrator',
      role: 'admin'
    },
    {
      username: 'doctor1',
      email: '<EMAIL>',
      password: 'doctor123',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      role: 'doctor'
    },
    {
      username: 'nurse1',
      email: '<EMAIL>',
      password: 'nurse123',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      role: 'nurse'
    },
    {
      username: 'staff1',
      email: '<EMAIL>',
      password: 'staff123',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      role: 'staff'
    },
    {
      username: 'receptionist1',
      email: '<EMAIL>',
      password: 'reception123',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      role: 'receptionist'
    }
  ];

  for (const user of users) {
    try {
      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(user.password, saltRounds);

      await database.run(`
        INSERT INTO users (username, email, password_hash, first_name, last_name, role)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        user.username,
        user.email,
        passwordHash,
        user.first_name,
        user.last_name,
        user.role
      ]);

      console.log(`Created user: ${user.username} (${user.role})`);
    } catch (error) {
      if (error.message.includes('UNIQUE constraint failed')) {
        console.log(`User ${user.username} already exists, skipping...`);
      } else {
        console.error(`Error creating user ${user.username}:`, error.message);
      }
    }
  }

  console.log('Users seeding completed!');
}

if (require.main === module) {
  database.init()
    .then(() => seedUsers())
    .then(() => {
      console.log('User seeding complete');
      process.exit(0);
    })
    .catch(err => {
      console.error('User seeding failed:', err);
      process.exit(1);
    });
}

module.exports = { seedUsers };
