const Appointment = require('../models/Appointment');
const Joi = require('joi');

const appointmentSchema = Joi.object({
  patient_id: Joi.number().integer().positive().required(),
  doctor_id: Joi.number().integer().positive().required(),
  appointment_date: Joi.date().iso().required(),
  appointment_time: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
  duration_minutes: Joi.number().integer().positive().default(30),
  reason: Joi.string().max(500).allow(null, ''),
  notes: Joi.string().max(2000).allow(null, ''),
  status: Joi.string().valid('scheduled', 'confirmed', 'completed', 'cancelled', 'no_show').default('scheduled'),
  appointment_type: Joi.string().valid('consultation', 'follow_up', 'emergency', 'surgery', 'checkup').default('consultation'),
  priority: Joi.string().valid('low', 'normal', 'high', 'urgent').default('normal'),
  create_billing: Joi.boolean().default(false),
  consultation_fee: Joi.number().positive().when('create_billing', { is: true, then: Joi.required() })
});

const updateAppointmentSchema = appointmentSchema.fork(
  ['patient_id', 'doctor_id', 'appointment_date', 'appointment_time'], 
  (schema) => schema.optional()
);

class AppointmentController {
  static async getAllAppointments(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        status, 
        doctor_id, 
        patient_id, 
        date_from, 
        date_to, 
        appointment_type 
      } = req.query;

      const offset = (page - 1) * limit;
      const filters = {};

      if (status) filters.status = status;
      if (doctor_id) filters.doctor_id = doctor_id;
      if (patient_id) filters.patient_id = patient_id;
      if (date_from) filters.date_from = date_from;
      if (date_to) filters.date_to = date_to;
      if (appointment_type) filters.appointment_type = appointment_type;

      const appointments = await Appointment.findAll(parseInt(limit), offset, filters);

      res.json({
        success: true,
        data: appointments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: appointments.length
        }
      });
    } catch (error) {
      console.error('Error fetching appointments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch appointments',
        message: error.message
      });
    }
  }

  static async getAppointmentById(req, res) {
    try {
      const { id } = req.params;
      const appointment = await Appointment.findById(id);

      if (!appointment) {
        return res.status(404).json({
          success: false,
          error: 'Appointment not found'
        });
      }

      res.json({
        success: true,
        data: appointment
      });
    } catch (error) {
      console.error('Error fetching appointment:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch appointment',
        message: error.message
      });
    }
  }

  static async createAppointment(req, res) {
    try {
      const { error, value } = appointmentSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const appointment = await Appointment.create(value);

      res.status(201).json({
        success: true,
        data: appointment,
        message: 'Appointment created successfully'
      });
    } catch (error) {
      console.error('Error creating appointment:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create appointment',
        message: error.message
      });
    }
  }

  static async bookAppointment(req, res) {
    try {
      const { error, value } = appointmentSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const appointment = await Appointment.bookAppointment(value);

      res.status(201).json({
        success: true,
        data: appointment,
        message: 'Appointment booked successfully'
      });
    } catch (error) {
      console.error('Error booking appointment:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to book appointment',
        message: error.message
      });
    }
  }

  static async updateAppointment(req, res) {
    try {
      const { id } = req.params;
      const { error, value } = updateAppointmentSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const appointment = await Appointment.update(id, value);

      if (!appointment) {
        return res.status(404).json({
          success: false,
          error: 'Appointment not found'
        });
      }

      res.json({
        success: true,
        data: appointment,
        message: 'Appointment updated successfully'
      });
    } catch (error) {
      console.error('Error updating appointment:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update appointment',
        message: error.message
      });
    }
  }

  static async deleteAppointment(req, res) {
    try {
      const { id } = req.params;
      const deleted = await Appointment.delete(id);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: 'Appointment not found'
        });
      }

      res.json({
        success: true,
        message: 'Appointment deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting appointment:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete appointment',
        message: error.message
      });
    }
  }

  static async getAppointmentStatistics(req, res) {
    try {
      const statistics = await Appointment.getStatistics();
      const byType = await Appointment.getAppointmentsByType();

      res.json({
        success: true,
        data: {
          overview: statistics,
          byType: byType
        }
      });
    } catch (error) {
      console.error('Error fetching appointment statistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch appointment statistics',
        message: error.message
      });
    }
  }

  static async getTodaysAppointments(req, res) {
    try {
      const appointments = await Appointment.getTodaysAppointments();

      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Error fetching today\'s appointments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch today\'s appointments',
        message: error.message
      });
    }
  }

  static async getUpcomingAppointments(req, res) {
    try {
      const { days = 7 } = req.query;
      const appointments = await Appointment.getUpcomingAppointments(parseInt(days));

      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Error fetching upcoming appointments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch upcoming appointments',
        message: error.message
      });
    }
  }

  static async getAppointmentsByDateRange(req, res) {
    try {
      const { start_date, end_date } = req.query;

      if (!start_date || !end_date) {
        return res.status(400).json({
          success: false,
          error: 'Start date and end date are required'
        });
      }

      const appointments = await Appointment.getAppointmentsByDate(start_date, end_date);

      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Error fetching appointments by date range:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch appointments by date range',
        message: error.message
      });
    }
  }
}

module.exports = AppointmentController;
