# Hospital Management System

A comprehensive Hospital Management System with a Node.js/Express backend and Flutter frontend.

## Features

### Backend (Node.js/Express)
- **Patient Management**: CRUD operations for patient records
- **Doctor Management**: Manage doctor profiles and specializations
- **Appointment Scheduling**: Book and manage appointments
- **Billing System**: Handle billing and payment tracking
- **Department Management**: Organize hospital departments
- **Prescription Management**: Track prescriptions and medications
- **Analytics Dashboard**: Comprehensive reporting and analytics
- **Authentication**: JWT-based user authentication
- **Database**: SQLite database with comprehensive schema

### Frontend (Flutter)
- **Responsive UI**: Works on web, mobile, and desktop
- **Interactive Dashboard**: Real-time statistics and overview
- **Patient Management**: Search, add, edit, and view patient details
- **Doctor Management**: Manage doctor profiles and information
- **Appointment Management**: Schedule and track appointments
- **Billing Interface**: Handle billing records and payments
- **Navigation**: Bottom navigation and drawer menu
- **Material Design**: Modern and intuitive user interface

## Project Structure

```
database/
├── backend/                 # Node.js/Express API server
│   ├── controllers/        # API controllers
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── middleware/        # Authentication middleware
│   ├── db/               # Database setup and migrations
│   └── server.js         # Main server file
├── hospital_management_flutter/  # Flutter frontend
│   ├── lib/
│   │   ├── screens/      # UI screens
│   │   ├── services/     # API service layer
│   │   └── main.dart     # Main app file
│   └── pubspec.yaml      # Flutter dependencies
├── build-and-run.bat     # Windows build script
├── build-and-run.sh      # Unix build script
└── README.md             # This file
```

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- Flutter SDK (latest stable)
- Git

### Backend Setup
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up the database:
   ```bash
   npm run setup
   ```

4. Seed the database with sample data:
   ```bash
   npm run seed
   ```

### Flutter Frontend Setup
1. Navigate to the Flutter directory:
   ```bash
   cd hospital_management_flutter
   ```

2. Get Flutter dependencies:
   ```bash
   flutter pub get
   ```

3. Enable web support (if not already enabled):
   ```bash
   flutter config --enable-web
   ```

## Running the Application

### Option 1: Integrated (Recommended)
Run both backend and frontend on the same port (3000):

**Windows:**
```bash
build-and-run.bat
```

**Unix/Linux/Mac:**
```bash
chmod +x build-and-run.sh
./build-and-run.sh
```

This will:
1. Build the Flutter web app
2. Start the backend server
3. Serve the Flutter app at `http://localhost:3000`

### Option 2: Separate Development Servers

**Backend:**
```bash
cd backend
npm run dev
```
Backend will run on `http://localhost:3000`

**Flutter (separate terminal):**
```bash
cd hospital_management_flutter
flutter run -d web-server --web-port 8080
```
Frontend will run on `http://localhost:8080`

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get user profile

### Patients
- `GET /api/patients` - Get all patients
- `POST /api/patients` - Create new patient
- `GET /api/patients/:id` - Get patient by ID
- `PUT /api/patients/:id` - Update patient
- `DELETE /api/patients/:id` - Delete patient

### Doctors
- `GET /api/doctors` - Get all doctors
- `POST /api/doctors` - Create new doctor
- `GET /api/doctors/:id` - Get doctor by ID
- `PUT /api/doctors/:id` - Update doctor
- `DELETE /api/doctors/:id` - Delete doctor

### Appointments
- `GET /api/appointments` - Get all appointments
- `POST /api/appointments` - Create new appointment
- `GET /api/appointments/:id` - Get appointment by ID
- `PUT /api/appointments/:id` - Update appointment
- `DELETE /api/appointments/:id` - Delete appointment

### Billing
- `GET /api/billing` - Get all billing records
- `POST /api/billing` - Create new billing record
- `GET /api/billing/:id` - Get billing record by ID
- `PUT /api/billing/:id` - Update billing record
- `DELETE /api/billing/:id` - Delete billing record

### Analytics
- `GET /api/analytics/dashboard` - Dashboard statistics
- `GET /api/analytics/patient-demographics` - Patient demographics
- `GET /api/analytics/doctor-performance` - Doctor performance metrics
- `GET /api/analytics/financial-summary` - Financial analytics

## Default Login Credentials

For testing purposes, you can use these default credentials:
- **Username:** admin
- **Password:** admin123

## Development

### Backend Development
- Use `npm run dev` for development with auto-reload
- Database file: `backend/db/hospital.db`
- Logs are displayed in the console

### Flutter Development
- Use `flutter run -d web-server` for hot reload during development
- The app automatically connects to the backend API
- Material Design components are used throughout

## Building for Production

### Flutter Web Build
```bash
cd hospital_management_flutter
flutter build web --web-renderer html
```

### Backend Production
```bash
cd backend
npm start
```

## Features Overview

### Dashboard
- Real-time statistics
- Quick action buttons
- Recent activity feed
- Overview cards for patients, doctors, appointments

### Patient Management
- Search and filter patients
- Add/edit patient information
- View detailed patient profiles
- Patient demographics tracking

### Doctor Management
- Doctor profiles with specializations
- Experience and qualification tracking
- Department assignments
- Performance metrics

### Appointment System
- Schedule new appointments
- View appointment calendar
- Status tracking (scheduled, completed, cancelled)
- Patient-doctor appointment history

### Billing System
- Generate billing records
- Payment status tracking
- Financial reporting
- Outstanding balance management

## Technology Stack

**Backend:**
- Node.js with Express.js
- SQLite database
- JWT authentication
- Joi validation
- CORS enabled
- Rate limiting
- Security headers (Helmet)

**Frontend:**
- Flutter (Dart)
- Material Design 3
- HTTP client for API calls
- Responsive design
- State management with StatefulWidget

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
