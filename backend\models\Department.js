const database = require('../db/database');

class Department {
  constructor(data) {
    Object.assign(this, data);
  }

  static async findAll(limit = 100, offset = 0, filters = {}) {
    let query = `
      SELECT d.*, 
             hd.first_name as head_doctor_first_name,
             hd.last_name as head_doctor_last_name,
             COUNT(doc.id) as doctor_count,
             COUNT(CASE WHEN doc.status = 'active' THEN 1 END) as active_doctor_count
      FROM departments d
      LEFT JOIN doctors hd ON d.head_doctor_id = hd.id
      LEFT JOIN doctors doc ON d.id = doc.department_id
    `;
    
    const conditions = [];
    const params = [];

    if (filters.status) {
      conditions.push('d.status = ?');
      params.push(filters.status);
    }

    if (filters.search) {
      conditions.push('(d.name LIKE ? OR d.description LIKE ?)');
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += `
      GROUP BY d.id
      ORDER BY d.name ASC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    
    const rows = await database.query(query, params);
    return rows.map(row => new Department(row));
  }

  static async findById(id) {
    const query = `
      SELECT d.*,
             hd.first_name as head_doctor_first_name,
             hd.last_name as head_doctor_last_name,
             hd.email as head_doctor_email,
             COUNT(doc.id) as total_doctors,
             COUNT(CASE WHEN doc.status = 'active' THEN 1 END) as active_doctors,
             AVG(doc.salary) as average_salary
      FROM departments d
      LEFT JOIN doctors hd ON d.head_doctor_id = hd.id
      LEFT JOIN doctors doc ON d.id = doc.department_id
      WHERE d.id = ?
      GROUP BY d.id
    `;
    
    const row = await database.get(query, [id]);
    return row ? new Department(row) : null;
  }

  static async create(departmentData) {
    const {
      name, description, head_doctor_id, budget, established_date,
      phone, email, location, status = 'active'
    } = departmentData;

    const query = `
      INSERT INTO departments (
        name, description, head_doctor_id, budget, established_date,
        phone, email, location, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.run(query, [
      name, description, head_doctor_id, budget, established_date,
      phone, email, location, status
    ]);

    return this.findById(result.id);
  }

  static async update(id, departmentData) {
    const fields = [];
    const params = [];

    Object.keys(departmentData).forEach(key => {
      if (departmentData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(departmentData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `UPDATE departments SET ${fields.join(', ')} WHERE id = ?`;
    await database.run(query, params);

    return this.findById(id);
  }

  static async delete(id) {
    const result = await database.run('DELETE FROM departments WHERE id = ?', [id]);
    return result.changes > 0;
  }

  static async getStatistics() {
    const stats = await database.get(`
      SELECT 
        COUNT(*) as total_departments,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_departments,
        AVG(budget) as average_budget,
        SUM(budget) as total_budget
      FROM departments
    `);

    return stats;
  }

  static async getDepartmentPerformance() {
    const query = `
      SELECT d.id, d.name,
             COUNT(DISTINCT doc.id) as doctor_count,
             COUNT(a.id) as total_appointments,
             COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_appointments,
             ROUND(COUNT(CASE WHEN a.status = 'completed' THEN 1 END) * 100.0 / 
                   NULLIF(COUNT(a.id), 0), 2) as completion_rate,
             SUM(b.total_amount) as total_revenue
      FROM departments d
      LEFT JOIN doctors doc ON d.id = doc.department_id
      LEFT JOIN appointments a ON doc.id = a.doctor_id
      LEFT JOIN billing b ON a.id = b.appointment_id
      WHERE d.status = 'active'
      GROUP BY d.id, d.name
      ORDER BY total_revenue DESC
    `;

    return database.query(query);
  }

  // Instance methods
  async getDoctors(status = null) {
    let query = `
      SELECT d.*, 
             COUNT(a.id) as appointment_count,
             COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_appointments
      FROM doctors d
      LEFT JOIN appointments a ON d.id = a.doctor_id
      WHERE d.department_id = ?
    `;

    const params = [this.id];

    if (status) {
      query += ' AND d.status = ?';
      params.push(status);
    }

    query += `
      GROUP BY d.id
      ORDER BY d.last_name, d.first_name
    `;

    return database.query(query, params);
  }

  async getAppointments(limit = 100, status = null) {
    let query = `
      SELECT a.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization
      FROM appointments a
      JOIN doctors d ON a.doctor_id = d.id
      JOIN patients p ON a.patient_id = p.id
      WHERE d.department_id = ?
    `;

    const params = [this.id];

    if (status) {
      query += ' AND a.status = ?';
      params.push(status);
    }

    query += `
      ORDER BY a.appointment_date DESC, a.appointment_time DESC
      LIMIT ?
    `;
    params.push(limit);

    return database.query(query, params);
  }

  async getRevenue(startDate = null, endDate = null) {
    let query = `
      SELECT 
        COUNT(b.id) as total_bills,
        SUM(b.total_amount) as total_revenue,
        SUM(b.paid_amount) as total_paid,
        SUM(b.balance_amount) as total_outstanding
      FROM billing b
      JOIN appointments a ON b.appointment_id = a.id
      JOIN doctors d ON a.doctor_id = d.id
      WHERE d.department_id = ?
    `;

    const params = [this.id];

    if (startDate && endDate) {
      query += ' AND b.billing_date BETWEEN ? AND ?';
      params.push(startDate, endDate);
    }

    return database.get(query, params);
  }

  async getWorkload(startDate, endDate) {
    const query = `
      SELECT 
        COUNT(a.id) as total_appointments,
        COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_appointments,
        COUNT(CASE WHEN a.status = 'cancelled' THEN 1 END) as cancelled_appointments,
        COUNT(DISTINCT a.patient_id) as unique_patients,
        COUNT(DISTINCT a.doctor_id) as active_doctors
      FROM appointments a
      JOIN doctors d ON a.doctor_id = d.id
      WHERE d.department_id = ?
      AND a.appointment_date BETWEEN ? AND ?
    `;

    return database.get(query, [this.id, startDate, endDate]);
  }
}

module.exports = Department;
