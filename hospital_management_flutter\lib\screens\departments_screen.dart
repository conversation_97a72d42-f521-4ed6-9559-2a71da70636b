import 'package:flutter/material.dart';
import '../services/api_service.dart';

class DepartmentsScreen extends StatefulWidget {
  const DepartmentsScreen({super.key});

  @override
  State<DepartmentsScreen> createState() => _DepartmentsScreenState();
}

class _DepartmentsScreenState extends State<DepartmentsScreen> {
  List<dynamic> departments = [];
  bool isLoading = true;
  String? error;
  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadDepartments();
  }

  Future<void> _loadDepartments() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final response = await ApiService.getDepartments();
      setState(() {
        departments = response['data'] ?? [];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  List<dynamic> get filteredDepartments {
    if (searchQuery.isEmpty) return departments;
    return departments.where((dept) {
      final name = (dept['name'] ?? '').toLowerCase();
      final description = (dept['description'] ?? '').toLowerCase();
      final query = searchQuery.toLowerCase();
      return name.contains(query) || description.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Departments'),
        backgroundColor: Colors.indigo[800],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDepartments,
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search departments...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, size: 64, color: Colors.red[400]),
                            const SizedBox(height: 16),
                            Text('Error: $error'),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadDepartments,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _buildDepartmentsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddDepartmentDialog(),
        backgroundColor: Colors.indigo[800],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildDepartmentsList() {
    final displayDepartments = filteredDepartments;
    
    if (displayDepartments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.business_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No departments found'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDepartments,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: displayDepartments.length,
        itemBuilder: (context, index) {
          final department = displayDepartments[index];
          return _buildDepartmentCard(department);
        },
      ),
    );
  }

  Widget _buildDepartmentCard(Map<String, dynamic> department) {
    final status = department['status'] ?? 'active';
    Color statusColor = status == 'active' ? Colors.green : Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.indigo[100],
          child: Icon(Icons.business, color: Colors.indigo[800]),
        ),
        title: Text(
          department['name'] ?? 'Unknown Department',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (department['description'] != null) 
              Text('📋 ${department['description']}'),
            if (department['location'] != null) 
              Text('📍 ${department['location']}'),
            if (department['phone'] != null) 
              Text('📞 ${department['phone']}'),
            if (department['email'] != null) 
              Text('📧 ${department['email']}'),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                status.toUpperCase(),
                style: TextStyle(
                  color: statusColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility),
                  SizedBox(width: 8),
                  Text('View Details'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          onSelected: (value) => _handleDepartmentAction(value, department),
        ),
        onTap: () => _showDepartmentDetails(department),
      ),
    );
  }

  void _handleDepartmentAction(String action, Map<String, dynamic> department) {
    switch (action) {
      case 'view':
        _showDepartmentDetails(department);
        break;
      case 'edit':
        _showEditDepartmentDialog(department);
        break;
      case 'delete':
        _confirmDeleteDepartment(department);
        break;
    }
  }

  void _showDepartmentDetails(Map<String, dynamic> department) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(department['name'] ?? 'Department Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Name', department['name']),
              _buildDetailRow('Description', department['description']),
              _buildDetailRow('Location', department['location']),
              _buildDetailRow('Phone', department['phone']),
              _buildDetailRow('Email', department['email']),
              _buildDetailRow('Budget', department['budget']?.toString()),
              _buildDetailRow('Established Date', department['established_date']),
              _buildDetailRow('Status', department['status']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, dynamic value) {
    if (value == null || value.toString().isEmpty) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value.toString())),
        ],
      ),
    );
  }

  void _showAddDepartmentDialog() {
    _showDepartmentForm();
  }

  void _showEditDepartmentDialog(Map<String, dynamic> department) {
    _showDepartmentForm(department: department);
  }

  void _showDepartmentForm({Map<String, dynamic>? department}) {
    final isEditing = department != null;
    final formKey = GlobalKey<FormState>();
    
    final nameController = TextEditingController(text: department?['name'] ?? '');
    final descriptionController = TextEditingController(text: department?['description'] ?? '');
    final locationController = TextEditingController(text: department?['location'] ?? '');
    final phoneController = TextEditingController(text: department?['phone'] ?? '');
    final emailController = TextEditingController(text: department?['email'] ?? '');
    final budgetController = TextEditingController(text: department?['budget']?.toString() ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Department' : 'Add New Department'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(labelText: 'Department Name *'),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: descriptionController,
                  decoration: const InputDecoration(labelText: 'Description'),
                  maxLines: 2,
                ),
                TextFormField(
                  controller: locationController,
                  decoration: const InputDecoration(labelText: 'Location'),
                ),
                TextFormField(
                  controller: phoneController,
                  decoration: const InputDecoration(labelText: 'Phone'),
                  keyboardType: TextInputType.phone,
                ),
                TextFormField(
                  controller: emailController,
                  decoration: const InputDecoration(labelText: 'Email'),
                  keyboardType: TextInputType.emailAddress,
                ),
                TextFormField(
                  controller: budgetController,
                  decoration: const InputDecoration(labelText: 'Budget'),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _saveDepartment(
              formKey,
              {
                'name': nameController.text,
                'description': descriptionController.text.isEmpty ? null : descriptionController.text,
                'location': locationController.text.isEmpty ? null : locationController.text,
                'phone': phoneController.text.isEmpty ? null : phoneController.text,
                'email': emailController.text.isEmpty ? null : emailController.text,
                'budget': budgetController.text.isEmpty ? null : double.tryParse(budgetController.text),
              },
              isEditing ? department['id'] : null,
            ),
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveDepartment(GlobalKey<FormState> formKey, Map<String, dynamic> data, int? departmentId) async {
    if (!formKey.currentState!.validate()) return;

    try {
      if (departmentId != null) {
        await ApiService.updateDepartment(departmentId, data);
      } else {
        await ApiService.createDepartment(data);
      }
      
      if (mounted) {
        Navigator.pop(context);
        _loadDepartments();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(departmentId != null ? 'Department updated successfully' : 'Department added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _confirmDeleteDepartment(Map<String, dynamic> department) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete ${department['name']}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _deleteDepartment(department['id']),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteDepartment(int departmentId) async {
    try {
      await ApiService.deleteDepartment(departmentId);
      if (mounted) {
        Navigator.pop(context);
        _loadDepartments();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Department deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
