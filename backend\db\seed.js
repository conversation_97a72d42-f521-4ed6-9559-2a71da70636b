const database = require('./database');

// Sample data arrays
const departments = [
  'Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 'Oncology',
  'Emergency Medicine', 'Internal Medicine', 'Surgery', 'Radiology', 'Pathology',
  'Anesthesiology', 'Dermatology', 'Psychiatry', 'Ophthalmology', 'ENT',
  'Urology', 'Gynecology', 'Gastroenterology', 'Pulmonology', 'Nephrology'
];

const firstNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const lastNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const specializations = [
  'Cardiologist', 'Neurologist', 'Orthopedic Surgeon', 'Pediatrician', 'Oncologist',
  'Emergency Physician', 'Internist', 'General Surgeon', 'Radiologist', 'Pathologist',
  'Anesthesiologist', 'Dermatologist', 'Psychiatrist', 'Ophthalmologist', 'ENT Specialist',
  'Urologist', 'Gynecologist', 'Gastroenterologist', 'Pulmonologist', 'Nephrologist'
];

const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
const genders = ['Male', 'Female', 'Other'];
const appointmentTypes = ['consultation', 'follow_up', 'emergency', 'surgery', 'checkup'];
const priorities = ['low', 'normal', 'high', 'urgent'];
const statuses = ['active', 'inactive'];

// Utility functions
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function generateEmail(firstName, lastName, domain = 'email.com', id = '') {
  // Sometimes return null to simulate missing data
  if (Math.random() < 0.1) return null;
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}${id}@${domain}`;
}

function generatePhone() {
  // Sometimes return null to simulate missing data
  if (Math.random() < 0.05) return null;
  return `+1${getRandomInt(100, 999)}${getRandomInt(100, 999)}${getRandomInt(1000, 9999)}`;
}

async function seedDepartments() {
  console.log('Seeding departments...');
  
  for (let i = 0; i < departments.length; i++) {
    const dept = departments[i];
    await database.run(`
      INSERT INTO departments (name, description, budget, established_date, phone, email, location, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      dept,
      `${dept} department providing specialized medical care`,
      getRandomInt(100000, 1000000),
      getRandomDate(new Date('2000-01-01'), new Date('2020-12-31')).toISOString().split('T')[0],
      generatePhone(),
      `${dept.toLowerCase().replace(' ', '')}@hospital.com`,
      `Floor ${getRandomInt(1, 10)}, Wing ${getRandomElement(['A', 'B', 'C'])}`,
      getRandomElement(statuses)
    ]);
  }
}

async function seedDoctors() {
  console.log('Seeding doctors...');
  
  for (let i = 0; i < 500; i++) {
    const firstName = getRandomElement(firstNames);
    const lastName = getRandomElement(lastNames);
    
    await database.run(`
      INSERT INTO doctors (first_name, last_name, email, phone, license_number, specialization, 
                          department_id, hire_date, salary, experience_years, qualification, 
                          address, date_of_birth, gender, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      firstName,
      lastName,
      generateEmail(firstName, lastName, 'hospital.com', i),
      generatePhone(),
      `LIC${getRandomInt(100000, 999999)}`,
      getRandomElement(specializations),
      getRandomInt(1, departments.length),
      getRandomDate(new Date('2010-01-01'), new Date('2023-12-31')).toISOString().split('T')[0],
      getRandomInt(80000, 300000),
      getRandomInt(1, 30),
      `MD, ${getRandomElement(['Harvard', 'Johns Hopkins', 'Mayo Clinic', 'Stanford', 'UCLA'])}`,
      `${getRandomInt(100, 9999)} ${getRandomElement(['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr'])}`,
      getRandomDate(new Date('1960-01-01'), new Date('1990-12-31')).toISOString().split('T')[0],
      getRandomElement(genders),
      getRandomElement([...statuses, 'on_leave'])
    ]);
  }
}

async function seedPatients() {
  console.log('Seeding patients...');
  
  for (let i = 0; i < 10000; i++) {
    const firstName = getRandomElement(firstNames);
    const lastName = getRandomElement(lastNames);
    
    await database.run(`
      INSERT INTO patients (first_name, last_name, email, phone, date_of_birth, gender, 
                           address, emergency_contact_name, emergency_contact_phone, blood_type, 
                           allergies, medical_history, insurance_number, insurance_provider, 
                           registration_date, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      firstName,
      lastName,
      generateEmail(firstName, lastName, 'email.com', i),
      generatePhone(),
      getRandomDate(new Date('1920-01-01'), new Date('2020-12-31')).toISOString().split('T')[0],
      getRandomElement(genders),
      `${getRandomInt(100, 9999)} ${getRandomElement(['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr'])}`,
      `${getRandomElement(firstNames)} ${getRandomElement(lastNames)}`,
      generatePhone(),
      Math.random() < 0.9 ? getRandomElement(bloodTypes) : null,
      Math.random() < 0.3 ? getRandomElement(['Penicillin', 'Peanuts', 'Shellfish', 'None known']) : null,
      Math.random() < 0.4 ? getRandomElement(['Diabetes', 'Hypertension', 'Asthma', 'Heart disease', 'None']) : null,
      `INS${getRandomInt(100000, 999999)}`,
      getRandomElement(['Blue Cross', 'Aetna', 'Cigna', 'UnitedHealth', 'Kaiser']),
      getRandomDate(new Date('2020-01-01'), new Date()).toISOString().split('T')[0],
      getRandomElement([...statuses, 'deceased'])
    ]);
    
    if (i % 1000 === 0) {
      console.log(`Seeded ${i} patients...`);
    }
  }
}

async function seedAppointments() {
  console.log('Seeding appointments...');

  // Get patient and doctor IDs
  const patients = await database.query('SELECT id FROM patients LIMIT 5000');
  const doctors = await database.query('SELECT id FROM doctors');

  for (let i = 0; i < 15000; i++) {
    const patient = getRandomElement(patients);
    const doctor = getRandomElement(doctors);
    const appointmentDate = getRandomDate(new Date('2023-01-01'), new Date('2024-12-31'));

    try {
      await database.run(`
        INSERT INTO appointments (patient_id, doctor_id, appointment_date, appointment_time,
                                duration_minutes, reason, notes, status, appointment_type, priority)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        patient.id,
        doctor.id,
        appointmentDate.toISOString().split('T')[0],
        `${getRandomInt(8, 17)}:${getRandomElement(['00', '15', '30', '45'])}`,
        getRandomElement([15, 30, 45, 60]),
        getRandomElement(['Routine checkup', 'Follow-up', 'Consultation', 'Emergency', 'Surgery consultation']),
        Math.random() < 0.5 ? 'Patient notes and observations' : null,
        getRandomElement(['scheduled', 'confirmed', 'completed', 'cancelled', 'no_show']),
        getRandomElement(appointmentTypes),
        getRandomElement(priorities)
      ]);
    } catch (error) {
      // Skip duplicate appointments (same doctor, date, time)
      continue;
    }

    if (i % 1000 === 0) {
      console.log(`Seeded ${i} appointments...`);
    }
  }
}

async function seedPrescriptions() {
  console.log('Seeding prescriptions...');

  const medications = [
    'Amoxicillin', 'Lisinopril', 'Metformin', 'Amlodipine', 'Metoprolol',
    'Omeprazole', 'Simvastatin', 'Losartan', 'Albuterol', 'Gabapentin'
  ];

  const appointments = await database.query('SELECT id, patient_id, doctor_id FROM appointments LIMIT 8000');

  for (let i = 0; i < 12000; i++) {
    const appointment = getRandomElement(appointments);

    await database.run(`
      INSERT INTO prescriptions (patient_id, doctor_id, appointment_id, medication_name,
                               dosage, frequency, duration_days, instructions, prescribed_date,
                               start_date, end_date, status, refills_remaining)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      appointment.patient_id,
      appointment.doctor_id,
      Math.random() < 0.8 ? appointment.id : null,
      getRandomElement(medications),
      `${getRandomInt(5, 500)}mg`,
      getRandomElement(['Once daily', 'Twice daily', 'Three times daily', 'As needed']),
      getRandomInt(7, 90),
      'Take with food. Complete full course.',
      getRandomDate(new Date('2023-01-01'), new Date()).toISOString().split('T')[0],
      getRandomDate(new Date('2023-01-01'), new Date()).toISOString().split('T')[0],
      getRandomDate(new Date('2023-02-01'), new Date('2024-12-31')).toISOString().split('T')[0],
      getRandomElement(['active', 'completed', 'discontinued']),
      getRandomInt(0, 5)
    ]);

    if (i % 1000 === 0) {
      console.log(`Seeded ${i} prescriptions...`);
    }
  }
}

async function seedBilling() {
  console.log('Seeding billing records...');

  const appointments = await database.query('SELECT id, patient_id FROM appointments LIMIT 10000');

  for (let i = 0; i < 10000; i++) {
    const appointment = getRandomElement(appointments);
    const totalAmount = getRandomInt(50, 5000);
    const paidAmount = Math.random() < 0.7 ? getRandomInt(0, totalAmount) : 0;

    await database.run(`
      INSERT INTO billing (patient_id, appointment_id, invoice_number, total_amount,
                          paid_amount, billing_date, due_date, payment_status,
                          payment_method, insurance_claim_amount, discount_amount,
                          tax_amount, description)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      appointment.patient_id,
      Math.random() < 0.8 ? appointment.id : null,
      `INV${Date.now()}${getRandomInt(1000, 9999)}`,
      totalAmount,
      paidAmount,
      getRandomDate(new Date('2023-01-01'), new Date()).toISOString().split('T')[0],
      getRandomDate(new Date(), new Date('2024-12-31')).toISOString().split('T')[0],
      paidAmount >= totalAmount ? 'paid' : paidAmount > 0 ? 'partial' : 'pending',
      paidAmount > 0 ? getRandomElement(['cash', 'card', 'insurance', 'bank_transfer']) : null,
      getRandomInt(0, totalAmount * 0.8),
      getRandomInt(0, totalAmount * 0.1),
      totalAmount * 0.08,
      getRandomElement(['Consultation fee', 'Surgery charges', 'Diagnostic tests', 'Emergency treatment'])
    ]);

    if (i % 1000 === 0) {
      console.log(`Seeded ${i} billing records...`);
    }
  }
}

async function main() {
  try {
    await database.init();

    await seedDepartments();
    await seedDoctors();
    await seedPatients();
    await seedAppointments();
    await seedPrescriptions();
    await seedBilling();

    console.log('Database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  seedDepartments,
  seedDoctors,
  seedPatients,
  seedAppointments,
  seedPrescriptions,
  seedBilling
};
