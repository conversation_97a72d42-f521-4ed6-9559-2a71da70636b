const Patient = require('../models/Patient');
const Joi = require('joi');

// Validation schemas
const patientSchema = Joi.object({
  first_name: Joi.string().min(1).max(50).required(),
  last_name: Joi.string().min(1).max(50).required(),
  email: Joi.string().email().allow(null, ''),
  phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).allow(null, ''),
  date_of_birth: Joi.date().iso().required(),
  gender: Joi.string().valid('Male', 'Female', 'Other').required(),
  address: Joi.string().max(500).allow(null, ''),
  emergency_contact_name: Joi.string().max(100).allow(null, ''),
  emergency_contact_phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).allow(null, ''),
  blood_type: Joi.string().valid('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-').allow(null, ''),
  allergies: Joi.string().max(1000).allow(null, ''),
  medical_history: Joi.string().max(2000).allow(null, ''),
  insurance_number: Joi.string().max(50).allow(null, ''),
  insurance_provider: Joi.string().max(100).allow(null, ''),
  status: Joi.string().valid('active', 'inactive', 'deceased').default('active')
});

const updatePatientSchema = patientSchema.fork(
  ['first_name', 'last_name', 'date_of_birth', 'gender'], 
  (schema) => schema.optional()
);

class PatientController {
  // GET /api/patients
  static async getAllPatients(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        status, 
        search, 
        blood_type 
      } = req.query;

      const offset = (page - 1) * limit;
      const filters = {};

      if (status) filters.status = status;
      if (search) filters.search = search;
      if (blood_type) filters.blood_type = blood_type;

      const patients = await Patient.findAll(parseInt(limit), offset, filters);

      res.json({
        success: true,
        data: patients,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: patients.length
        }
      });
    } catch (error) {
      console.error('Error fetching patients:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch patients',
        message: error.message
      });
    }
  }

  // GET /api/patients/:id
  static async getPatientById(req, res) {
    try {
      const { id } = req.params;
      const patient = await Patient.findById(id);

      if (!patient) {
        return res.status(404).json({
          success: false,
          error: 'Patient not found'
        });
      }

      res.json({
        success: true,
        data: patient
      });
    } catch (error) {
      console.error('Error fetching patient:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch patient',
        message: error.message
      });
    }
  }

  // POST /api/patients
  static async createPatient(req, res) {
    try {
      const { error, value } = patientSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const patient = await Patient.create(value);

      res.status(201).json({
        success: true,
        data: patient,
        message: 'Patient created successfully'
      });
    } catch (error) {
      console.error('Error creating patient:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create patient',
        message: error.message
      });
    }
  }

  // PUT /api/patients/:id
  static async updatePatient(req, res) {
    try {
      const { id } = req.params;
      const { error, value } = updatePatientSchema.validate(req.body);
      
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details.map(detail => detail.message)
        });
      }

      const patient = await Patient.update(id, value);

      if (!patient) {
        return res.status(404).json({
          success: false,
          error: 'Patient not found'
        });
      }

      res.json({
        success: true,
        data: patient,
        message: 'Patient updated successfully'
      });
    } catch (error) {
      console.error('Error updating patient:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update patient',
        message: error.message
      });
    }
  }

  // DELETE /api/patients/:id
  static async deletePatient(req, res) {
    try {
      const { id } = req.params;
      const deleted = await Patient.delete(id);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: 'Patient not found'
        });
      }

      res.json({
        success: true,
        message: 'Patient deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting patient:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete patient',
        message: error.message
      });
    }
  }

  // GET /api/patients/statistics
  static async getPatientStatistics(req, res) {
    try {
      const statistics = await Patient.getStatistics();
      const ageGroups = await Patient.getPatientsByAgeGroup();

      res.json({
        success: true,
        data: {
          overview: statistics,
          ageGroups: ageGroups
        }
      });
    } catch (error) {
      console.error('Error fetching patient statistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch patient statistics',
        message: error.message
      });
    }
  }

  // GET /api/patients/:id/appointments
  static async getPatientAppointments(req, res) {
    try {
      const { id } = req.params;
      const { limit = 10 } = req.query;

      const patient = await Patient.findById(id);
      if (!patient) {
        return res.status(404).json({
          success: false,
          error: 'Patient not found'
        });
      }

      const appointments = await patient.getAppointments(parseInt(limit));

      res.json({
        success: true,
        data: appointments
      });
    } catch (error) {
      console.error('Error fetching patient appointments:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch patient appointments',
        message: error.message
      });
    }
  }

  // GET /api/patients/:id/prescriptions
  static async getPatientPrescriptions(req, res) {
    try {
      const { id } = req.params;
      const { limit = 10 } = req.query;

      const patient = await Patient.findById(id);
      if (!patient) {
        return res.status(404).json({
          success: false,
          error: 'Patient not found'
        });
      }

      const prescriptions = await patient.getPrescriptions(parseInt(limit));

      res.json({
        success: true,
        data: prescriptions
      });
    } catch (error) {
      console.error('Error fetching patient prescriptions:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch patient prescriptions',
        message: error.message
      });
    }
  }

  // GET /api/patients/:id/billing
  static async getPatientBilling(req, res) {
    try {
      const { id } = req.params;
      const { limit = 10 } = req.query;

      const patient = await Patient.findById(id);
      if (!patient) {
        return res.status(404).json({
          success: false,
          error: 'Patient not found'
        });
      }

      const billingHistory = await patient.getBillingHistory(parseInt(limit));

      res.json({
        success: true,
        data: billingHistory
      });
    } catch (error) {
      console.error('Error fetching patient billing:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch patient billing',
        message: error.message
      });
    }
  }
}

module.exports = PatientController;
