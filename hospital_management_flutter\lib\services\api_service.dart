import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'http://localhost:3000/api';
  static String? _authToken;

  // Set authentication token
  static void setAuthToken(String token) {
    _authToken = token;
  }

  // Clear authentication token
  static void clearAuthToken() {
    _authToken = null;
  }

  // Get headers with authentication
  static Map<String, String> _getHeaders() {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
    };
    
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    
    return headers;
  }

  // Generic GET request
  static Future<Map<String, dynamic>> get(String endpoint) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: _getHeaders(),
      );
      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Generic POST request
  static Future<Map<String, dynamic>> post(String endpoint, Map<String, dynamic> data) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: _getHeaders(),
        body: jsonEncode(data),
      );
      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Generic PUT request
  static Future<Map<String, dynamic>> put(String endpoint, Map<String, dynamic> data) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: _getHeaders(),
        body: jsonEncode(data),
      );
      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Generic DELETE request
  static Future<Map<String, dynamic>> delete(String endpoint) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl$endpoint'),
        headers: _getHeaders(),
      );
      return _handleResponse(response);
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Handle HTTP response
  static Map<String, dynamic> _handleResponse(http.Response response) {
    final Map<String, dynamic> data = jsonDecode(response.body);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      throw Exception(data['error'] ?? 'Unknown error occurred');
    }
  }

  // Authentication APIs
  static Future<Map<String, dynamic>> login(String username, String password) async {
    return await post('/auth/login', {
      'username': username,
      'password': password,
    });
  }

  static Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
    return await post('/auth/register', userData);
  }

  static Future<Map<String, dynamic>> getProfile() async {
    return await get('/auth/me');
  }

  // Dashboard APIs
  static Future<Map<String, dynamic>> getDashboardStats() async {
    return await get('/analytics/dashboard');
  }

  // Patient APIs
  static Future<Map<String, dynamic>> getPatients() async {
    return await get('/patients');
  }

  static Future<Map<String, dynamic>> getPatient(int id) async {
    return await get('/patients/$id');
  }

  static Future<Map<String, dynamic>> createPatient(Map<String, dynamic> patientData) async {
    return await post('/patients', patientData);
  }

  static Future<Map<String, dynamic>> updatePatient(int id, Map<String, dynamic> patientData) async {
    return await put('/patients/$id', patientData);
  }

  static Future<Map<String, dynamic>> deletePatient(int id) async {
    return await delete('/patients/$id');
  }

  static Future<Map<String, dynamic>> getPatientStatistics() async {
    return await get('/patients/statistics');
  }

  // Doctor APIs
  static Future<Map<String, dynamic>> getDoctors() async {
    return await get('/doctors');
  }

  static Future<Map<String, dynamic>> getDoctor(int id) async {
    return await get('/doctors/$id');
  }

  static Future<Map<String, dynamic>> createDoctor(Map<String, dynamic> doctorData) async {
    return await post('/doctors', doctorData);
  }

  static Future<Map<String, dynamic>> updateDoctor(int id, Map<String, dynamic> doctorData) async {
    return await put('/doctors/$id', doctorData);
  }

  static Future<Map<String, dynamic>> deleteDoctor(int id) async {
    return await delete('/doctors/$id');
  }

  static Future<Map<String, dynamic>> getDoctorStatistics() async {
    return await get('/doctors/statistics');
  }

  static Future<Map<String, dynamic>> getAvailableDoctors() async {
    return await get('/doctors/available');
  }

  // Appointment APIs
  static Future<Map<String, dynamic>> getAppointments() async {
    return await get('/appointments');
  }

  static Future<Map<String, dynamic>> getAppointment(int id) async {
    return await get('/appointments/$id');
  }

  static Future<Map<String, dynamic>> createAppointment(Map<String, dynamic> appointmentData) async {
    return await post('/appointments', appointmentData);
  }

  static Future<Map<String, dynamic>> updateAppointment(int id, Map<String, dynamic> appointmentData) async {
    return await put('/appointments/$id', appointmentData);
  }

  static Future<Map<String, dynamic>> deleteAppointment(int id) async {
    return await delete('/appointments/$id');
  }

  static Future<Map<String, dynamic>> getTodaysAppointments() async {
    return await get('/appointments/today');
  }

  static Future<Map<String, dynamic>> getUpcomingAppointments() async {
    return await get('/appointments/upcoming');
  }

  static Future<Map<String, dynamic>> getAppointmentStatistics() async {
    return await get('/appointments/statistics');
  }

  // Department APIs
  static Future<Map<String, dynamic>> getDepartments() async {
    return await get('/departments');
  }

  static Future<Map<String, dynamic>> getDepartment(int id) async {
    return await get('/departments/$id');
  }

  static Future<Map<String, dynamic>> createDepartment(Map<String, dynamic> departmentData) async {
    return await post('/departments', departmentData);
  }

  static Future<Map<String, dynamic>> updateDepartment(int id, Map<String, dynamic> departmentData) async {
    return await put('/departments/$id', departmentData);
  }

  static Future<Map<String, dynamic>> deleteDepartment(int id) async {
    return await delete('/departments/$id');
  }

  // Billing APIs
  static Future<Map<String, dynamic>> getBilling() async {
    return await get('/billing');
  }

  static Future<Map<String, dynamic>> getBillingById(int id) async {
    return await get('/billing/$id');
  }

  static Future<Map<String, dynamic>> createBilling(Map<String, dynamic> billingData) async {
    return await post('/billing', billingData);
  }

  static Future<Map<String, dynamic>> updateBilling(int id, Map<String, dynamic> billingData) async {
    return await put('/billing/$id', billingData);
  }

  static Future<Map<String, dynamic>> deleteBilling(int id) async {
    return await delete('/billing/$id');
  }

  static Future<Map<String, dynamic>> getBillingStatistics() async {
    return await get('/billing/statistics');
  }

  static Future<Map<String, dynamic>> getRevenueAnalytics() async {
    return await get('/billing/revenue');
  }

  // Prescription APIs
  static Future<Map<String, dynamic>> getPrescriptions() async {
    return await get('/prescriptions');
  }

  static Future<Map<String, dynamic>> getPrescription(int id) async {
    return await get('/prescriptions/$id');
  }

  static Future<Map<String, dynamic>> createPrescription(Map<String, dynamic> prescriptionData) async {
    return await post('/prescriptions', prescriptionData);
  }

  static Future<Map<String, dynamic>> updatePrescription(int id, Map<String, dynamic> prescriptionData) async {
    return await put('/prescriptions/$id', prescriptionData);
  }

  static Future<Map<String, dynamic>> deletePrescription(int id) async {
    return await delete('/prescriptions/$id');
  }

  static Future<Map<String, dynamic>> getPrescriptionStatistics() async {
    return await get('/prescriptions/statistics');
  }

  // Analytics APIs
  static Future<Map<String, dynamic>> getPatientDemographics() async {
    return await get('/analytics/patient-demographics');
  }

  static Future<Map<String, dynamic>> getDoctorPerformance() async {
    return await get('/analytics/doctor-performance');
  }

  static Future<Map<String, dynamic>> getFinancialSummary() async {
    return await get('/analytics/financial-summary');
  }

  static Future<Map<String, dynamic>> getAppointmentTrends() async {
    return await get('/analytics/appointment-trends');
  }

  // Health check
  static Future<Map<String, dynamic>> healthCheck() async {
    try {
      final response = await http.get(Uri.parse('http://localhost:3000/health'));
      return jsonDecode(response.body);
    } catch (e) {
      throw Exception('Backend server is not running: $e');
    }
  }
}
