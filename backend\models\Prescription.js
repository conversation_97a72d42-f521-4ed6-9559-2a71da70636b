const database = require('../db/database');

class Prescription {
  constructor(data) {
    Object.assign(this, data);
  }

  static async findAll(limit = 100, offset = 0, filters = {}) {
    let query = `
      SELECT pr.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.date_of_birth,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization, d.license_number,
             a.appointment_date, a.reason as appointment_reason
      FROM prescriptions pr
      JOIN patients p ON pr.patient_id = p.id
      JOIN doctors d ON pr.doctor_id = d.id
      LEFT JOIN appointments a ON pr.appointment_id = a.id
    `;
    
    const conditions = [];
    const params = [];

    if (filters.status) {
      conditions.push('pr.status = ?');
      params.push(filters.status);
    }

    if (filters.patient_id) {
      conditions.push('pr.patient_id = ?');
      params.push(filters.patient_id);
    }

    if (filters.doctor_id) {
      conditions.push('pr.doctor_id = ?');
      params.push(filters.doctor_id);
    }

    if (filters.medication_name) {
      conditions.push('pr.medication_name LIKE ?');
      params.push(`%${filters.medication_name}%`);
    }

    if (filters.date_from) {
      conditions.push('pr.prescribed_date >= ?');
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      conditions.push('pr.prescribed_date <= ?');
      params.push(filters.date_to);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += `
      ORDER BY pr.prescribed_date DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    
    const rows = await database.query(query, params);
    return rows.map(row => new Prescription(row));
  }

  static async findById(id) {
    const query = `
      SELECT pr.*,
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.email as patient_email,
             p.date_of_birth, p.allergies, p.medical_history,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization, d.license_number, d.phone as doctor_phone,
             a.appointment_date, a.reason as appointment_reason
      FROM prescriptions pr
      JOIN patients p ON pr.patient_id = p.id
      JOIN doctors d ON pr.doctor_id = d.id
      LEFT JOIN appointments a ON pr.appointment_id = a.id
      WHERE pr.id = ?
    `;
    
    const row = await database.get(query, [id]);
    return row ? new Prescription(row) : null;
  }

  static async create(prescriptionData) {
    const {
      patient_id, doctor_id, appointment_id, medication_name, dosage,
      frequency, duration_days, instructions, prescribed_date,
      start_date, end_date, status = 'active', refills_remaining = 0,
      pharmacy_notes
    } = prescriptionData;

    const query = `
      INSERT INTO prescriptions (
        patient_id, doctor_id, appointment_id, medication_name, dosage,
        frequency, duration_days, instructions, prescribed_date,
        start_date, end_date, status, refills_remaining, pharmacy_notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.run(query, [
      patient_id, doctor_id, appointment_id, medication_name, dosage,
      frequency, duration_days, instructions, prescribed_date,
      start_date, end_date, status, refills_remaining, pharmacy_notes
    ]);

    return this.findById(result.id);
  }

  static async update(id, prescriptionData) {
    const fields = [];
    const params = [];

    Object.keys(prescriptionData).forEach(key => {
      if (prescriptionData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(prescriptionData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `UPDATE prescriptions SET ${fields.join(', ')} WHERE id = ?`;
    await database.run(query, params);

    return this.findById(id);
  }

  static async delete(id) {
    const result = await database.run('DELETE FROM prescriptions WHERE id = ?', [id]);
    return result.changes > 0;
  }

  static async getStatistics() {
    const stats = await database.get(`
      SELECT 
        COUNT(*) as total_prescriptions,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_prescriptions,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_prescriptions,
        COUNT(CASE WHEN status = 'discontinued' THEN 1 END) as discontinued_prescriptions,
        COUNT(DISTINCT medication_name) as unique_medications,
        COUNT(DISTINCT patient_id) as patients_with_prescriptions,
        AVG(duration_days) as average_duration_days
      FROM prescriptions
    `);

    return stats;
  }

  static async getMostPrescribedMedications(limit = 10) {
    const query = `
      SELECT 
        medication_name,
        COUNT(*) as prescription_count,
        COUNT(DISTINCT patient_id) as unique_patients,
        COUNT(DISTINCT doctor_id) as prescribing_doctors
      FROM prescriptions
      GROUP BY medication_name
      ORDER BY prescription_count DESC
      LIMIT ?
    `;

    return database.query(query, [limit]);
  }

  static async getPrescriptionsByDoctor(limit = 10) {
    const query = `
      SELECT 
        d.id, d.first_name, d.last_name, d.specialization,
        COUNT(pr.id) as total_prescriptions,
        COUNT(DISTINCT pr.medication_name) as unique_medications,
        COUNT(DISTINCT pr.patient_id) as unique_patients
      FROM doctors d
      LEFT JOIN prescriptions pr ON d.id = pr.doctor_id
      GROUP BY d.id
      ORDER BY total_prescriptions DESC
      LIMIT ?
    `;

    return database.query(query, [limit]);
  }

  static async getActivePrescriptions() {
    const query = `
      SELECT pr.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name
      FROM prescriptions pr
      JOIN patients p ON pr.patient_id = p.id
      JOIN doctors d ON pr.doctor_id = d.id
      WHERE pr.status = 'active'
      AND (pr.end_date IS NULL OR pr.end_date >= date('now'))
      ORDER BY pr.prescribed_date DESC
    `;

    const rows = await database.query(query);
    return rows.map(row => new Prescription(row));
  }

  static async getExpiringPrescriptions(days = 7) {
    const query = `
      SELECT pr.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.email as patient_email,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name
      FROM prescriptions pr
      JOIN patients p ON pr.patient_id = p.id
      JOIN doctors d ON pr.doctor_id = d.id
      WHERE pr.status = 'active'
      AND pr.end_date BETWEEN date('now') AND date('now', '+${days} days')
      ORDER BY pr.end_date ASC
    `;

    const rows = await database.query(query);
    return rows.map(row => new Prescription(row));
  }

  static async getDrugInteractionCheck(patientId, newMedication) {
    // This is a simplified drug interaction check
    // In a real system, you'd have a comprehensive drug interaction database
    const query = `
      SELECT pr.medication_name, pr.dosage, pr.frequency,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name
      FROM prescriptions pr
      JOIN doctors d ON pr.doctor_id = d.id
      WHERE pr.patient_id = ?
      AND pr.status = 'active'
      AND (pr.end_date IS NULL OR pr.end_date >= date('now'))
    `;

    const activePrescriptions = await database.query(query, [patientId]);
    
    // Simple interaction warnings (in a real system, this would be more sophisticated)
    const interactions = [];
    const commonInteractions = {
      'Warfarin': ['Aspirin', 'Ibuprofen'],
      'Metformin': ['Alcohol'],
      'Lisinopril': ['Potassium supplements'],
      'Simvastatin': ['Grapefruit']
    };

    activePrescriptions.forEach(prescription => {
      if (commonInteractions[prescription.medication_name]?.includes(newMedication) ||
          commonInteractions[newMedication]?.includes(prescription.medication_name)) {
        interactions.push({
          medication: prescription.medication_name,
          interaction: `Potential interaction between ${prescription.medication_name} and ${newMedication}`,
          severity: 'moderate'
        });
      }
    });

    return {
      activePrescriptions,
      interactions,
      hasInteractions: interactions.length > 0
    };
  }

  static async refillPrescription(id) {
    return database.transaction(async (db) => {
      const prescription = await this.findById(id);
      if (!prescription) {
        throw new Error('Prescription not found');
      }

      if (prescription.refills_remaining <= 0) {
        throw new Error('No refills remaining');
      }

      if (prescription.status !== 'active') {
        throw new Error('Prescription is not active');
      }

      await db.run(`
        UPDATE prescriptions 
        SET refills_remaining = refills_remaining - 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [id]);

      return this.findById(id);
    });
  }

  // Instance methods
  async discontinue(reason = null) {
    const notes = reason ? `Discontinued: ${reason}` : 'Discontinued';
    return Prescription.update(this.id, {
      status: 'discontinued',
      pharmacy_notes: this.pharmacy_notes ? `${this.pharmacy_notes}\n${notes}` : notes
    });
  }

  async complete() {
    return Prescription.update(this.id, { status: 'completed' });
  }

  isExpired() {
    if (!this.end_date) return false;
    const today = new Date();
    const endDate = new Date(this.end_date);
    return endDate < today;
  }

  isExpiringSoon(days = 7) {
    if (!this.end_date) return false;
    const today = new Date();
    const endDate = new Date(this.end_date);
    const warningDate = new Date(today.getTime() + (days * 24 * 60 * 60 * 1000));
    return endDate <= warningDate && endDate >= today;
  }

  canRefill() {
    return this.status === 'active' && this.refills_remaining > 0 && !this.isExpired();
  }
}

module.exports = Prescription;
