const database = require('../db/database');

class Billing {
  constructor(data) {
    Object.assign(this, data);
  }

  static async findAll(limit = 100, offset = 0, filters = {}) {
    let query = `
      SELECT b.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.email as patient_email,
             a.appointment_date, a.reason as appointment_reason,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name
      FROM billing b
      JOIN patients p ON b.patient_id = p.id
      LEFT JOIN appointments a ON b.appointment_id = a.id
      LEFT JOIN doctors d ON a.doctor_id = d.id
    `;
    
    const conditions = [];
    const params = [];

    if (filters.payment_status) {
      conditions.push('b.payment_status = ?');
      params.push(filters.payment_status);
    }

    if (filters.patient_id) {
      conditions.push('b.patient_id = ?');
      params.push(filters.patient_id);
    }

    if (filters.date_from) {
      conditions.push('b.billing_date >= ?');
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      conditions.push('b.billing_date <= ?');
      params.push(filters.date_to);
    }

    if (filters.overdue) {
      conditions.push('b.due_date < date("now") AND b.payment_status != "paid"');
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += `
      ORDER BY b.billing_date DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    
    const rows = await database.query(query, params);
    return rows.map(row => new Billing(row));
  }

  static async findById(id) {
    const query = `
      SELECT b.*,
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.email as patient_email,
             p.address as patient_address, p.insurance_provider,
             a.appointment_date, a.reason as appointment_reason,
             d.first_name as doctor_first_name, d.last_name as doctor_last_name,
             d.specialization
      FROM billing b
      JOIN patients p ON b.patient_id = p.id
      LEFT JOIN appointments a ON b.appointment_id = a.id
      LEFT JOIN doctors d ON a.doctor_id = d.id
      WHERE b.id = ?
    `;
    
    const row = await database.get(query, [id]);
    return row ? new Billing(row) : null;
  }

  static async create(billingData) {
    const {
      patient_id, appointment_id, invoice_number, total_amount,
      paid_amount = 0, billing_date, due_date, payment_status = 'pending',
      payment_method, insurance_claim_amount = 0, discount_amount = 0,
      tax_amount = 0, description, notes
    } = billingData;

    const query = `
      INSERT INTO billing (
        patient_id, appointment_id, invoice_number, total_amount,
        paid_amount, billing_date, due_date, payment_status,
        payment_method, insurance_claim_amount, discount_amount,
        tax_amount, description, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.run(query, [
      patient_id, appointment_id, invoice_number, total_amount,
      paid_amount, billing_date, due_date, payment_status,
      payment_method, insurance_claim_amount, discount_amount,
      tax_amount, description, notes
    ]);

    return this.findById(result.id);
  }

  static async update(id, billingData) {
    const fields = [];
    const params = [];

    Object.keys(billingData).forEach(key => {
      if (billingData[key] !== undefined && key !== 'balance_amount') {
        fields.push(`${key} = ?`);
        params.push(billingData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    // Update payment status based on paid amount
    if (billingData.paid_amount !== undefined) {
      const current = await this.findById(id);
      const totalAmount = billingData.total_amount || current.total_amount;
      const paidAmount = billingData.paid_amount;

      if (paidAmount >= totalAmount) {
        fields.push('payment_status = ?');
        params.push('paid');
      } else if (paidAmount > 0) {
        fields.push('payment_status = ?');
        params.push('partial');
      }
    }

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const query = `UPDATE billing SET ${fields.join(', ')} WHERE id = ?`;
    await database.run(query, params);

    return this.findById(id);
  }

  static async delete(id) {
    const result = await database.run('DELETE FROM billing WHERE id = ?', [id]);
    return result.changes > 0;
  }

  static async processPayment(id, paymentData) {
    return database.transaction(async (db) => {
      const billing = await this.findById(id);
      if (!billing) {
        throw new Error('Billing record not found');
      }

      const { amount, payment_method, notes } = paymentData;
      const newPaidAmount = parseFloat(billing.paid_amount) + parseFloat(amount);
      const totalAmount = parseFloat(billing.total_amount);

      let payment_status = 'partial';
      if (newPaidAmount >= totalAmount) {
        payment_status = 'paid';
      }

      await db.run(`
        UPDATE billing 
        SET paid_amount = ?, payment_status = ?, payment_method = ?, 
            notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [newPaidAmount, payment_status, payment_method, notes, id]);

      // Log payment transaction (you could create a payments table for this)
      // For now, we'll just update the billing record

      return this.findById(id);
    });
  }

  static async getStatistics() {
    const stats = await database.get(`
      SELECT 
        COUNT(*) as total_bills,
        SUM(total_amount) as total_billed,
        SUM(paid_amount) as total_paid,
        SUM(balance_amount) as total_outstanding,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_bills,
        COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_bills,
        COUNT(CASE WHEN payment_status = 'partial' THEN 1 END) as partial_bills,
        COUNT(CASE WHEN payment_status = 'overdue' THEN 1 END) as overdue_bills,
        AVG(total_amount) as average_bill_amount
      FROM billing
    `);

    return stats;
  }

  static async getRevenueAnalytics(startDate, endDate) {
    const query = `
      SELECT 
        DATE(billing_date) as date,
        COUNT(*) as bill_count,
        SUM(total_amount) as total_revenue,
        SUM(paid_amount) as collected_revenue,
        SUM(balance_amount) as outstanding_revenue
      FROM billing
      WHERE billing_date BETWEEN ? AND ?
      GROUP BY DATE(billing_date)
      ORDER BY date
    `;

    return database.query(query, [startDate, endDate]);
  }

  static async getPaymentMethodAnalytics() {
    const query = `
      SELECT 
        payment_method,
        COUNT(*) as count,
        SUM(paid_amount) as total_amount
      FROM billing
      WHERE payment_method IS NOT NULL
      GROUP BY payment_method
      ORDER BY total_amount DESC
    `;

    return database.query(query);
  }

  static async getOverdueBills() {
    const query = `
      SELECT b.*, 
             p.first_name as patient_first_name, p.last_name as patient_last_name,
             p.phone as patient_phone, p.email as patient_email,
             JULIANDAY('now') - JULIANDAY(b.due_date) as days_overdue
      FROM billing b
      JOIN patients p ON b.patient_id = p.id
      WHERE b.due_date < date('now') 
      AND b.payment_status != 'paid'
      ORDER BY days_overdue DESC
    `;

    const rows = await database.query(query);
    return rows.map(row => new Billing(row));
  }

  static async getTopDebtors(limit = 10) {
    const query = `
      SELECT p.id, p.first_name, p.last_name, p.phone, p.email,
             SUM(b.balance_amount) as total_debt,
             COUNT(b.id) as outstanding_bills
      FROM patients p
      JOIN billing b ON p.id = b.patient_id
      WHERE b.payment_status != 'paid'
      GROUP BY p.id
      ORDER BY total_debt DESC
      LIMIT ?
    `;

    return database.query(query, [limit]);
  }

  static async getInsuranceClaimAnalytics() {
    const query = `
      SELECT 
        p.insurance_provider,
        COUNT(*) as claim_count,
        SUM(b.insurance_claim_amount) as total_claimed,
        AVG(b.insurance_claim_amount) as average_claim
      FROM billing b
      JOIN patients p ON b.patient_id = p.id
      WHERE b.insurance_claim_amount > 0
      AND p.insurance_provider IS NOT NULL
      GROUP BY p.insurance_provider
      ORDER BY total_claimed DESC
    `;

    return database.query(query);
  }

  // Instance methods
  async getPaymentHistory() {
    // This would require a separate payments table in a real system
    // For now, we'll return the billing record itself
    return [this];
  }

  async sendReminder() {
    // In a real system, this would send email/SMS reminders
    // For now, we'll just update the notes
    const reminderNote = `Payment reminder sent on ${new Date().toISOString()}`;
    const currentNotes = this.notes || '';
    const updatedNotes = currentNotes ? `${currentNotes}\n${reminderNote}` : reminderNote;

    return Billing.update(this.id, { notes: updatedNotes });
  }

  isOverdue() {
    const today = new Date();
    const dueDate = new Date(this.due_date);
    return dueDate < today && this.payment_status !== 'paid';
  }

  getDaysOverdue() {
    if (!this.isOverdue()) return 0;
    
    const today = new Date();
    const dueDate = new Date(this.due_date);
    const diffTime = today - dueDate;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}

module.exports = Billing;
