import 'package:flutter/material.dart';
import '../services/api_service.dart';

class AppointmentsScreen extends StatefulWidget {
  const AppointmentsScreen({super.key});

  @override
  State<AppointmentsScreen> createState() => _AppointmentsScreenState();
}

class _AppointmentsScreenState extends State<AppointmentsScreen> {
  List<dynamic> appointments = [];
  bool isLoading = true;
  String? error;
  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final response = await ApiService.getAppointments();
      setState(() {
        appointments = response['data'] ?? [];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  List<dynamic> get filteredAppointments {
    if (searchQuery.isEmpty) return appointments;
    return appointments.where((appointment) {
      final patientName = (appointment['patient_name'] ?? '').toLowerCase();
      final doctorName = (appointment['doctor_name'] ?? '').toLowerCase();
      final type = (appointment['appointment_type'] ?? '').toLowerCase();
      final query = searchQuery.toLowerCase();
      return patientName.contains(query) || doctorName.contains(query) || type.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Appointments'),
        backgroundColor: Colors.orange[800],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAppointments,
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search appointments...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, size: 64, color: Colors.red[400]),
                            const SizedBox(height: 16),
                            Text('Error: $error'),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadAppointments,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _buildAppointmentsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddAppointmentDialog(),
        backgroundColor: Colors.orange[800],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildAppointmentsList() {
    final displayAppointments = filteredAppointments;
    
    if (displayAppointments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.calendar_today_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No appointments found'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAppointments,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: displayAppointments.length,
        itemBuilder: (context, index) {
          final appointment = displayAppointments[index];
          return _buildAppointmentCard(appointment);
        },
      ),
    );
  }

  Widget _buildAppointmentCard(Map<String, dynamic> appointment) {
    final status = appointment['status'] ?? 'scheduled';
    Color statusColor = Colors.blue;
    IconData statusIcon = Icons.schedule;

    switch (status.toLowerCase()) {
      case 'completed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'cancelled':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      case 'in_progress':
        statusColor = Colors.orange;
        statusIcon = Icons.play_circle;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: statusColor.withValues(alpha: 0.1),
          child: Icon(statusIcon, color: statusColor),
        ),
        title: Text(
          appointment['patient_name'] ?? 'Unknown Patient',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (appointment['doctor_name'] != null) 
              Text('👨‍⚕️ Dr. ${appointment['doctor_name']}'),
            if (appointment['appointment_date'] != null) 
              Text('📅 ${appointment['appointment_date']}'),
            if (appointment['appointment_time'] != null) 
              Text('🕐 ${appointment['appointment_time']}'),
            if (appointment['appointment_type'] != null) 
              Text('📋 ${appointment['appointment_type']}'),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                status.toUpperCase(),
                style: TextStyle(
                  color: statusColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility),
                  SizedBox(width: 8),
                  Text('View Details'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            if (status != 'completed')
              const PopupMenuItem(
                value: 'complete',
                child: Row(
                  children: [
                    Icon(Icons.check, color: Colors.green),
                    SizedBox(width: 8),
                    Text('Mark Complete', style: TextStyle(color: Colors.green)),
                  ],
                ),
              ),
            if (status != 'cancelled')
              const PopupMenuItem(
                value: 'cancel',
                child: Row(
                  children: [
                    Icon(Icons.cancel, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Cancel', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          onSelected: (value) => _handleAppointmentAction(value, appointment),
        ),
        onTap: () => _showAppointmentDetails(appointment),
      ),
    );
  }

  void _handleAppointmentAction(String action, Map<String, dynamic> appointment) {
    switch (action) {
      case 'view':
        _showAppointmentDetails(appointment);
        break;
      case 'edit':
        _showEditAppointmentDialog(appointment);
        break;
      case 'complete':
        _updateAppointmentStatus(appointment['id'], 'completed');
        break;
      case 'cancel':
        _updateAppointmentStatus(appointment['id'], 'cancelled');
        break;
      case 'delete':
        _confirmDeleteAppointment(appointment);
        break;
    }
  }

  void _showAppointmentDetails(Map<String, dynamic> appointment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Appointment Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Patient', appointment['patient_name']),
              _buildDetailRow('Doctor', appointment['doctor_name']),
              _buildDetailRow('Date', appointment['appointment_date']),
              _buildDetailRow('Time', appointment['appointment_time']),
              _buildDetailRow('Type', appointment['appointment_type']),
              _buildDetailRow('Duration', '${appointment['duration_minutes']} minutes'),
              _buildDetailRow('Status', appointment['status']),
              _buildDetailRow('Notes', appointment['notes']),
              _buildDetailRow('Reason', appointment['reason']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, dynamic value) {
    if (value == null || value.toString().isEmpty) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value.toString())),
        ],
      ),
    );
  }

  void _showAddAppointmentDialog() {
    _showAppointmentForm();
  }

  void _showEditAppointmentDialog(Map<String, dynamic> appointment) {
    _showAppointmentForm(appointment: appointment);
  }

  void _showAppointmentForm({Map<String, dynamic>? appointment}) {
    final isEditing = appointment != null;
    final formKey = GlobalKey<FormState>();
    
    final patientIdController = TextEditingController(text: appointment?['patient_id']?.toString() ?? '');
    final doctorIdController = TextEditingController(text: appointment?['doctor_id']?.toString() ?? '');
    final dateController = TextEditingController(text: appointment?['appointment_date'] ?? '');
    final timeController = TextEditingController(text: appointment?['appointment_time'] ?? '');
    final typeController = TextEditingController(text: appointment?['appointment_type'] ?? '');
    final reasonController = TextEditingController(text: appointment?['reason'] ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Appointment' : 'New Appointment'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: patientIdController,
                  decoration: const InputDecoration(labelText: 'Patient ID'),
                  keyboardType: TextInputType.number,
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: doctorIdController,
                  decoration: const InputDecoration(labelText: 'Doctor ID'),
                  keyboardType: TextInputType.number,
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: dateController,
                  decoration: const InputDecoration(
                    labelText: 'Date (YYYY-MM-DD)',
                    hintText: '2024-01-15',
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: timeController,
                  decoration: const InputDecoration(
                    labelText: 'Time (HH:MM)',
                    hintText: '14:30',
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Required' : null,
                ),
                TextFormField(
                  controller: typeController,
                  decoration: const InputDecoration(labelText: 'Appointment Type'),
                ),
                TextFormField(
                  controller: reasonController,
                  decoration: const InputDecoration(labelText: 'Reason'),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _saveAppointment(
              formKey,
              {
                'patient_id': int.tryParse(patientIdController.text),
                'doctor_id': int.tryParse(doctorIdController.text),
                'appointment_date': dateController.text,
                'appointment_time': timeController.text,
                'appointment_type': typeController.text,
                'reason': reasonController.text,
              },
              isEditing ? appointment['id'] : null,
            ),
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveAppointment(GlobalKey<FormState> formKey, Map<String, dynamic> data, int? appointmentId) async {
    if (!formKey.currentState!.validate()) return;

    try {
      if (appointmentId != null) {
        await ApiService.updateAppointment(appointmentId, data);
      } else {
        await ApiService.createAppointment(data);
      }
      
      if (mounted) {
        Navigator.pop(context);
        _loadAppointments();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(appointmentId != null ? 'Appointment updated successfully' : 'Appointment created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateAppointmentStatus(int appointmentId, String status) async {
    try {
      await ApiService.updateAppointment(appointmentId, {'status': status});
      _loadAppointments();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Appointment $status successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _confirmDeleteAppointment(Map<String, dynamic> appointment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this appointment?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _deleteAppointment(appointment['id']),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteAppointment(int appointmentId) async {
    try {
      await ApiService.deleteAppointment(appointmentId);
      if (mounted) {
        Navigator.pop(context);
        _loadAppointments();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
