const database = require('./database');
const { 
  seedDepartments, 
  seedDoctors, 
  seedPatients, 
  seedAppointments, 
  seedPrescriptions, 
  seedBilling 
} = require('./seed');

async function setupDatabase() {
  try {
    console.log('🏥 Setting up Hospital Management System Database...\n');

    // Initialize database and create tables
    console.log('📋 Initializing database and creating tables...');
    await database.init();
    console.log('✅ Database initialized successfully\n');

    // Seed data
    console.log('🌱 Seeding database with sample data...');
    
    console.log('  📊 Seeding departments...');
    await seedDepartments();
    console.log('  ✅ Departments seeded\n');

    console.log('  👨‍⚕️ Seeding doctors...');
    await seedDoctors();
    console.log('  ✅ Doctors seeded\n');

    console.log('  👥 Seeding patients (this may take a while)...');
    await seedPatients();
    console.log('  ✅ Patients seeded\n');

    console.log('  📅 Seeding appointments...');
    await seedAppointments();
    console.log('  ✅ Appointments seeded\n');

    console.log('  💊 Seeding prescriptions...');
    await seedPrescriptions();
    console.log('  ✅ Prescriptions seeded\n');

    console.log('  💰 Seeding billing records...');
    await seedBilling();
    console.log('  ✅ Billing records seeded\n');

    // Display summary
    const summary = await database.get(`
      SELECT 
        (SELECT COUNT(*) FROM departments) as departments,
        (SELECT COUNT(*) FROM doctors) as doctors,
        (SELECT COUNT(*) FROM patients) as patients,
        (SELECT COUNT(*) FROM appointments) as appointments,
        (SELECT COUNT(*) FROM prescriptions) as prescriptions,
        (SELECT COUNT(*) FROM billing) as billing_records
    `);

    console.log('📈 Database Setup Complete!');
    console.log('='.repeat(50));
    console.log(`📊 Departments:     ${summary.departments.toLocaleString()}`);
    console.log(`👨‍⚕️ Doctors:         ${summary.doctors.toLocaleString()}`);
    console.log(`👥 Patients:        ${summary.patients.toLocaleString()}`);
    console.log(`📅 Appointments:    ${summary.appointments.toLocaleString()}`);
    console.log(`💊 Prescriptions:   ${summary.prescriptions.toLocaleString()}`);
    console.log(`💰 Billing Records: ${summary.billing_records.toLocaleString()}`);
    console.log('='.repeat(50));
    console.log('\n🚀 Your Hospital Management System is ready to use!');
    console.log('💡 Start the server with: npm start');
    console.log('🔧 Development mode: npm run dev');

  } catch (error) {
    console.error('❌ Error setting up database:', error);
    process.exit(1);
  } finally {
    database.close();
    process.exit(0);
  }
}

if (require.main === module) {
  setupDatabase();
}

module.exports = setupDatabase;
